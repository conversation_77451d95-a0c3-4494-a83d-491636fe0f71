# TrOCR OCR 功能使用指南

本指南介绍如何在LLM-Kit项目中使用基于TrOCR的OCR功能。

## 📋 概述

TrOCR (Transformer-based Optical Character Recognition) 是微软开发的基于Transformer架构的OCR模型，具有以下优势：

- **高精度**: 基于Vision Transformer和文本Transformer的端到端架构
- **多场景支持**: 支持印刷文本、手写文本、场景文本等
- **易于集成**: 基于Hugging Face Transformers，易于部署和使用
- **多语言支持**: 支持多种语言的文本识别

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装新增的依赖
pip install sentencepiece protobuf

# 或者重新安装所有依赖
pip install -r requirements.txt
```

### 2. 基本使用

```python
from text_parse.parse import single_ocr, batch_ocr
from PIL import Image

# 单张图片OCR
image = Image.open("test_image.png")
result = single_ocr(image, model_name='base', use_trocr=True)
print(f"识别结果: {result}")

# 批量图片OCR
images = [Image.open(f"image_{i}.png") for i in range(3)]
results = batch_ocr(images, model_name='base', use_trocr=True)
for i, result in enumerate(results):
    print(f"图片 {i+1}: {result}")
```

### 3. 使用OCR服务

```python
from text_parse.ocr_service import OCRService
from text_parse.ocr_config import get_ocr_config

# 创建OCR服务
config = get_ocr_config('high_quality')
ocr_service = OCRService(config)

# 单张图片识别
result = ocr_service.recognize_single("image.png")

# 批量识别
results = ocr_service.recognize_batch(["img1.png", "img2.png"])

# PDF文档OCR
pdf_results = ocr_service.recognize_pdf_pages("document.pdf")
```

## 🔧 配置选项

### 可用的TrOCR模型

| 模型名称 | 描述 | 适用场景 |
|---------|------|----------|
| `base` | 基础印刷文本模型 | 一般印刷文档 |
| `large` | 大型印刷文本模型 | 高质量印刷文档 |
| `handwritten` | 手写文本模型 | 手写文档 |
| `stage1` | 预训练模型 | 特殊场景 |
| `str` | 场景文本模型 | 自然场景中的文本 |

### 预设配置

| 配置名称 | 描述 | 特点 |
|---------|------|------|
| `default` | 默认配置 | 平衡性能和质量 |
| `printed_text` | 印刷文本配置 | 高质量印刷文档 |
| `handwritten_text` | 手写文本配置 | 手写文档优化 |
| `scene_text` | 场景文本配置 | 自然场景文本 |
| `fast_processing` | 快速处理配置 | 速度优先 |
| `high_quality` | 高质量配置 | 质量优先 |
| `low_memory` | 低内存配置 | 内存受限环境 |

### 自定义配置

```python
from text_parse.ocr_config import create_custom_config

# 创建自定义配置
custom_config = create_custom_config(
    model_name='large',
    batch_size=2,
    max_length=1024,
    num_beams=6,
    resize_images=True,
    target_height=384,
    enhance_contrast=True
)
```

## 🌐 API接口

### 1. 获取OCR状态

```bash
GET /ocr/status
```

### 2. 列出可用模型

```bash
GET /ocr/models
```

### 3. 单张图片OCR

```bash
POST /ocr/single
Content-Type: multipart/form-data

file: [图片文件]
model_name: base
use_trocr: true
config_name: default
```

### 4. 批量图片OCR

```bash
POST /ocr/batch
Content-Type: multipart/form-data

files: [图片文件列表]
model_name: base
use_trocr: true
config_name: default
```

### 5. Base64图片OCR

```bash
POST /ocr/base64
Content-Type: application/x-www-form-urlencoded

image_data: [base64编码的图片数据]
model_name: base
use_trocr: true
```

### 6. PDF文档OCR

```bash
POST /ocr/pdf
Content-Type: multipart/form-data

file: [PDF文件]
model_name: base
use_trocr: true
start_page: 0
end_page: 10
```

## 📝 使用示例

### 示例1: 处理扫描文档

```python
from text_parse.ocr_service import OCRService
from text_parse.ocr_config import get_ocr_config

# 使用高质量配置处理扫描文档
config = get_ocr_config('high_quality')
ocr_service = OCRService(config)

# 处理PDF文档
results = ocr_service.recognize_pdf_pages("scanned_document.pdf")

# 保存结果
with open("extracted_text.txt", "w", encoding="utf-8") as f:
    for i, text in enumerate(results):
        f.write(f"=== 第 {i+1} 页 ===\n")
        f.write(text)
        f.write("\n\n")
```

### 示例2: 批量处理图片

```python
import os
from text_parse.parse import batch_ocr
from PIL import Image

# 获取所有图片文件
image_dir = "images/"
image_files = [os.path.join(image_dir, f) for f in os.listdir(image_dir) 
               if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

# 加载图片
images = [Image.open(f) for f in image_files]

# 批量OCR
results = batch_ocr(images, model_name='large', use_trocr=True)

# 保存结果
for filename, result in zip(image_files, results):
    print(f"{os.path.basename(filename)}: {result}")
```

### 示例3: 使用API接口

```python
import requests
import base64

# 读取图片并转换为base64
with open("test_image.png", "rb") as f:
    image_data = base64.b64encode(f.read()).decode()

# 调用API
response = requests.post(
    "http://localhost:8000/ocr/base64",
    data={
        "image_data": image_data,
        "model_name": "large",
        "use_trocr": True,
        "config_name": "high_quality"
    }
)

if response.status_code == 200:
    result = response.json()
    print(f"识别结果: {result['data']['recognized_text']}")
else:
    print(f"请求失败: {response.status_code}")
```

## 🔍 故障排除

### 常见问题

1. **模型下载失败**
   - 检查网络连接
   - 设置Hugging Face镜像源
   - 使用代理或VPN

2. **内存不足**
   - 使用`low_memory`配置
   - 减小`batch_size`
   - 使用较小的模型（如`base`而非`large`）

3. **识别精度不高**
   - 尝试不同的模型（如手写文本使用`handwritten`）
   - 启用图片预处理（`resize_images`, `enhance_contrast`）
   - 使用`high_quality`配置

4. **处理速度慢**
   - 使用`fast_processing`配置
   - 减少`num_beams`
   - 使用GPU加速

### 环境变量配置

```bash
# 设置OCR相关环境变量
export OCR_MODEL_NAME=base
export OCR_DEVICE=cuda
export OCR_BATCH_SIZE=4
export OCR_USE_TROCR=true
export OCR_CACHE_DIR=/path/to/cache
```

## 🧪 测试

运行测试脚本验证OCR功能：

```bash
python test_trocr_ocr.py
```

测试包括：
- TrOCR引擎测试
- 便捷函数测试
- 配置测试
- OCR服务测试
- 文件OCR测试
- API接口测试

## 📚 参考资料

- [TrOCR论文](https://arxiv.org/abs/2109.10282)
- [Hugging Face TrOCR文档](https://huggingface.co/docs/transformers/model_doc/trocr)
- [TrOCR模型列表](https://huggingface.co/models?search=trocr)

## 🤝 贡献

如果您发现问题或有改进建议，请：

1. 提交Issue描述问题
2. 提供复现步骤
3. 如果可能，提供修复方案

## 📄 许可证

本项目遵循原项目的许可证条款。
