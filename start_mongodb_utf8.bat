@echo off
:: Set UTF-8 encoding for proper display
chcp 65001 >nul 2>&1

:: Set console colors
color 0B

echo.
echo ==========================================
echo         MongoDB Database Launcher
echo ==========================================
echo.

:: Change to script directory
cd /d "%~dp0"

:: Display system information
echo [SYSTEM] Checking system environment...
echo [SYSTEM] Current directory: %cd%
echo [SYSTEM] Date/Time: %date% %time%
echo.

:: Check if MongoDB directory exists
echo [CHECK] Verifying MongoDB installation...
if not exist "mongodb\bin\mongod.exe" (
    echo [ERROR] MongoDB not found!
    echo [ERROR] MongoDB executable not found at: %cd%\mongodb\bin\mongod.exe
    echo.
    echo [SOLUTION] To install MongoDB:
    echo [SOLUTION] 1. Run: python setup_environment.py
    echo [SOLUTION] 2. Or manually download MongoDB and extract to 'mongodb' folder
    echo [SOLUTION] 3. Ensure mongod.exe is at: mongodb\bin\mongod.exe
    echo.
    pause
    exit /b 1
) else (
    echo [SUCCESS] MongoDB executable found
    :: Get MongoDB version
    mongodb\bin\mongod.exe --version | findstr "db version" > temp_version.txt 2>nul
    if exist temp_version.txt (
        for /f "tokens=*" %%i in (temp_version.txt) do echo [INFO] %%i
        del temp_version.txt >nul 2>&1
    )
)
echo.

:: Create necessary directories
echo [SETUP] Creating required directories...
for %%d in (mongodb_data mongodb_logs) do (
    if not exist "%%d" (
        mkdir "%%d" >nul 2>&1
        echo [CREATED] Directory: %%d
    ) else (
        echo [EXISTS] Directory: %%d
    )
)
echo.

:: Check configuration file
echo [CHECK] Verifying MongoDB configuration...
if not exist "mongod.conf" (
    echo [ERROR] MongoDB configuration file not found!
    echo [ERROR] mongod.conf file is missing from: %cd%
    echo.
    echo [SOLUTION] To fix this issue:
    echo [SOLUTION] 1. Ensure mongod.conf exists in the project directory
    echo [SOLUTION] 2. Or run the setup script to create default configuration
    echo.
    pause
    exit /b 1
) else (
    echo [SUCCESS] Configuration file found: mongod.conf
)
echo.

:: Check if MongoDB is already running
echo [CHECK] Checking if MongoDB is already running...
netstat -an | findstr ":27017" >nul 2>&1
if not errorlevel 1 (
    echo [WARNING] Port 27017 is already in use!
    echo [WARNING] MongoDB may already be running
    echo.
    echo [OPTION] Choose an option:
    echo [OPTION] 1. Continue anyway ^(may cause conflicts^)
    echo [OPTION] 2. Exit and check existing MongoDB process
    echo.
    choice /c 12 /m "Enter your choice (1 or 2): "
    if errorlevel 2 (
        echo [INFO] Exiting. Please check running processes and try again.
        pause
        exit /b 1
    )
    echo [INFO] Continuing with startup...
    echo.
)

:: Display MongoDB configuration
echo [CONFIG] MongoDB configuration:
echo [CONFIG] - Data directory: %cd%\mongodb_data
echo [CONFIG] - Log directory: %cd%\mongodb_logs
echo [CONFIG] - Configuration file: %cd%\mongod.conf
echo [CONFIG] - Default port: 27017
echo [CONFIG] - Bind IP: 127.0.0.1
echo.

:: Check disk space
echo [CHECK] Checking available disk space...
for /f "tokens=3" %%i in ('dir /-c %cd% 2^>nul ^| findstr "bytes free"') do (
    echo [INFO] Available disk space: %%i bytes
)
echo.

:: Final startup confirmation
echo [READY] All checks completed. Starting MongoDB...
echo.
echo ==========================================
echo            MongoDB Service Info
echo ==========================================
echo [ACCESS] Connection URL: mongodb://localhost:27017
echo [ACCESS] Database name: llm_kit ^(default^)
echo [LOGS] Log file: mongodb_logs\mongod.log
echo [DATA] Data directory: mongodb_data\
echo ==========================================
echo.
echo [INFO] Press Ctrl+C to stop MongoDB
echo [INFO] MongoDB logs will be displayed below
echo [INFO] Keep this window open while using LLM-Kit
echo.

:: Start MongoDB
echo [STARTING] Launching MongoDB server...
echo.
mongodb\bin\mongod.exe --config mongod.conf

:: Handle MongoDB exit
echo.
echo ==========================================
echo [STOPPED] MongoDB server has stopped
echo ==========================================
echo.
echo [INFO] If MongoDB stopped unexpectedly, check the error messages above
echo [INFO] Common issues and solutions:
echo [INFO] - Port already in use: Stop other MongoDB instances
echo [INFO] - Permission errors: Run as administrator
echo [INFO] - Disk space: Ensure sufficient disk space available
echo [INFO] - Configuration errors: Check mongod.conf file
echo.
echo [INFO] Check log file for details: mongodb_logs\mongod.log
echo.
echo [INFO] Press any key to exit...
pause >nul
