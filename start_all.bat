@echo off
:: One-click startup script for LLM-Kit (MongoDB + Application)
chcp 65001 >nul 2>&1

echo.
echo ========================================
echo     LLM-Kit One-Click Startup
echo ========================================
echo.

:: Change to project directory
cd /d "d:\pythonprojs\LLM-Kit"
echo [INFO] Working directory: %cd%
echo.

:: Check basic requirements
echo [CHECK] Verifying requirements...

if not exist "main.py" (
    echo [ERROR] main.py not found
    pause
    exit /b 1
)

python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found
    pause
    exit /b 1
)

echo [SUCCESS] Basic requirements OK
echo.

:: Create directories
echo [SETUP] Creating directories...
for %%d in (uploads temp outputs logs cache models_cache mongodb_data mongodb_logs) do (
    if not exist "%%d" mkdir "%%d"
)
echo [SUCCESS] Directories ready
echo.

:: Check if MongoDB binary exists
if exist "mongodb\bin\mongod.exe" (
    echo [INFO] MongoDB binary found, will start local MongoDB
    set USE_LOCAL_MONGODB=1
) else (
    echo [WARNING] MongoDB binary not found at mongodb\bin\mongod.exe
    echo [INFO] Will try to use system MongoDB or continue without it
    set USE_LOCAL_MONGODB=0
)
echo.

:: Start MongoDB if available
if "%USE_LOCAL_MONGODB%"=="1" (
    echo [STARTING] Starting local MongoDB...
    
    :: Create simple config if needed
    if not exist "mongod.conf" (
        echo storage: > mongod.conf
        echo   dbPath: ./mongodb_data >> mongod.conf
        echo systemLog: >> mongod.conf
        echo   destination: file >> mongod.conf
        echo   path: ./mongodb_logs/mongod.log >> mongod.conf
        echo net: >> mongod.conf
        echo   port: 27017 >> mongod.conf
        echo   bindIp: 127.0.0.1 >> mongod.conf
    )
    
    :: Start MongoDB in background
    start "MongoDB Server" /min mongodb\bin\mongod.exe --config mongod.conf
    
    :: Wait a moment for MongoDB to start
    echo [INFO] Waiting for MongoDB to start...
    timeout /t 3 /nobreak >nul
    
    :: Test MongoDB connection
    python -c "import pymongo; client = pymongo.MongoClient('mongodb://localhost:27017', serverSelectionTimeoutMS=5000); client.admin.command('ping'); print('[SUCCESS] MongoDB started successfully')" 2>nul
    if errorlevel 1 (
        echo [WARNING] MongoDB may not be fully ready yet
    )
) else (
    echo [INFO] Skipping MongoDB startup
)
echo.

:: Install missing packages if needed
echo [CHECK] Checking Python packages...
python -c "import fastapi, uvicorn, motor, pymongo" 2>nul
if errorlevel 1 (
    echo [INFO] Installing missing packages...
    pip install fastapi uvicorn motor pymongo loguru
)
echo.

:: Initialize database if needed
if exist "init_database.py" (
    echo [SETUP] Initializing database...
    python init_database.py
    echo.
)

:: Start the application
echo [STARTING] Starting LLM-Kit application...
echo.
echo ========================================
echo          Application Ready
echo ========================================
echo.
echo Access URLs:
echo - Main Application: http://127.0.0.1:8000
echo - API Documentation: http://127.0.0.1:8000/docs
echo - Interactive API: http://127.0.0.1:8000/redoc
echo - OCR Status: http://127.0.0.1:8000/ocr/status
echo.
echo Press Ctrl+C to stop the application
echo MongoDB will continue running in background
echo ========================================
echo.

:: Start the main application
python main.py

:: Cleanup message
echo.
echo ========================================
echo [STOPPED] LLM-Kit application stopped
echo ========================================
echo.
echo [INFO] MongoDB is still running in background
echo [INFO] To stop MongoDB, close the MongoDB Server window
echo [INFO] Or run: taskkill /f /im mongod.exe
echo.
pause
