"""
OCR Configuration and utility functions for TrOCR integration
"""

import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class OCRConfig:
    """Configuration class for OCR settings"""
    
    # Model settings
    model_name: str = 'base'  # TrOCR model variant
    device: Optional[str] = None  # Device to run on (auto-detect if None)
    cache_dir: Optional[str] = None  # Model cache directory
    
    # Processing settings
    use_trocr: bool = True  # Whether to use TrOCR (fallback to GOT-OCR2_0 if False)
    batch_size: int = 4  # Batch size for processing multiple images
    max_length: int = 512  # Maximum length of generated text
    num_beams: int = 4  # Number of beams for beam search
    early_stopping: bool = True  # Whether to stop early in beam search
    
    # Image preprocessing settings
    resize_images: bool = True  # Whether to resize images for better OCR
    target_height: int = 384  # Target height for image resizing
    enhance_contrast: bool = False  # Whether to enhance image contrast
    
    # Performance settings
    enable_gpu: bool = True  # Whether to use GPU if available
    low_memory_mode: bool = False  # Whether to use low memory mode
    
    # Fallback settings
    fallback_to_got_ocr: bool = True  # Whether to fallback to GOT-OCR2_0
    retry_attempts: int = 3  # Number of retry attempts for failed OCR
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary"""
        return {
            'model_name': self.model_name,
            'device': self.device,
            'cache_dir': self.cache_dir,
            'use_trocr': self.use_trocr,
            'batch_size': self.batch_size,
            'max_length': self.max_length,
            'num_beams': self.num_beams,
            'early_stopping': self.early_stopping,
            'resize_images': self.resize_images,
            'target_height': self.target_height,
            'enhance_contrast': self.enhance_contrast,
            'enable_gpu': self.enable_gpu,
            'low_memory_mode': self.low_memory_mode,
            'fallback_to_got_ocr': self.fallback_to_got_ocr,
            'retry_attempts': self.retry_attempts
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'OCRConfig':
        """Create config from dictionary"""
        return cls(**config_dict)
    
    @classmethod
    def from_env(cls) -> 'OCRConfig':
        """Create config from environment variables"""
        return cls(
            model_name=os.getenv('OCR_MODEL_NAME', 'base'),
            device=os.getenv('OCR_DEVICE'),
            cache_dir=os.getenv('OCR_CACHE_DIR'),
            use_trocr=os.getenv('OCR_USE_TROCR', 'true').lower() == 'true',
            batch_size=int(os.getenv('OCR_BATCH_SIZE', '4')),
            max_length=int(os.getenv('OCR_MAX_LENGTH', '512')),
            num_beams=int(os.getenv('OCR_NUM_BEAMS', '4')),
            early_stopping=os.getenv('OCR_EARLY_STOPPING', 'true').lower() == 'true',
            resize_images=os.getenv('OCR_RESIZE_IMAGES', 'true').lower() == 'true',
            target_height=int(os.getenv('OCR_TARGET_HEIGHT', '384')),
            enhance_contrast=os.getenv('OCR_ENHANCE_CONTRAST', 'false').lower() == 'true',
            enable_gpu=os.getenv('OCR_ENABLE_GPU', 'true').lower() == 'true',
            low_memory_mode=os.getenv('OCR_LOW_MEMORY_MODE', 'false').lower() == 'true',
            fallback_to_got_ocr=os.getenv('OCR_FALLBACK_TO_GOT_OCR', 'true').lower() == 'true',
            retry_attempts=int(os.getenv('OCR_RETRY_ATTEMPTS', '3'))
        )


# Default OCR configuration
DEFAULT_OCR_CONFIG = OCRConfig()

# Model-specific configurations for different use cases
OCR_CONFIGS = {
    'printed_text': OCRConfig(
        model_name='large',
        max_length=1024,
        num_beams=6,
        resize_images=True,
        target_height=384
    ),
    
    'handwritten_text': OCRConfig(
        model_name='handwritten',
        max_length=512,
        num_beams=4,
        resize_images=True,
        target_height=384,
        enhance_contrast=True
    ),
    
    'scene_text': OCRConfig(
        model_name='str',
        max_length=256,
        num_beams=3,
        resize_images=True,
        target_height=256
    ),
    
    'fast_processing': OCRConfig(
        model_name='base',
        max_length=256,
        num_beams=2,
        batch_size=8,
        early_stopping=True
    ),
    
    'high_quality': OCRConfig(
        model_name='large',
        max_length=1024,
        num_beams=8,
        batch_size=2,
        early_stopping=False,
        enhance_contrast=True
    ),
    
    'low_memory': OCRConfig(
        model_name='base',
        max_length=256,
        num_beams=2,
        batch_size=1,
        low_memory_mode=True,
        resize_images=True,
        target_height=256
    )
}


def get_ocr_config(config_name: str = 'default') -> OCRConfig:
    """
    Get OCR configuration by name
    
    Args:
        config_name: Name of the configuration
        
    Returns:
        OCRConfig instance
    """
    if config_name == 'default':
        return DEFAULT_OCR_CONFIG
    elif config_name in OCR_CONFIGS:
        return OCR_CONFIGS[config_name]
    else:
        logger.warning(f"Unknown OCR config: {config_name}, using default")
        return DEFAULT_OCR_CONFIG


def list_available_configs() -> list:
    """List all available OCR configurations"""
    return ['default'] + list(OCR_CONFIGS.keys())


def validate_ocr_config(config: OCRConfig) -> bool:
    """
    Validate OCR configuration
    
    Args:
        config: OCRConfig instance to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        # Check model name
        valid_models = ['base', 'large', 'handwritten', 'stage1', 'str']
        if config.model_name not in valid_models:
            logger.error(f"Invalid model name: {config.model_name}")
            return False
        
        # Check batch size
        if config.batch_size < 1:
            logger.error(f"Invalid batch size: {config.batch_size}")
            return False
        
        # Check max length
        if config.max_length < 1:
            logger.error(f"Invalid max length: {config.max_length}")
            return False
        
        # Check num beams
        if config.num_beams < 1:
            logger.error(f"Invalid num beams: {config.num_beams}")
            return False
        
        # Check target height
        if config.target_height < 32:
            logger.error(f"Invalid target height: {config.target_height}")
            return False
        
        # Check retry attempts
        if config.retry_attempts < 1:
            logger.error(f"Invalid retry attempts: {config.retry_attempts}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating OCR config: {str(e)}")
        return False


def create_custom_config(**kwargs) -> OCRConfig:
    """
    Create custom OCR configuration
    
    Args:
        **kwargs: Configuration parameters
        
    Returns:
        OCRConfig instance
    """
    # Start with default config
    config_dict = DEFAULT_OCR_CONFIG.to_dict()
    
    # Update with custom parameters
    config_dict.update(kwargs)
    
    # Create and validate config
    config = OCRConfig.from_dict(config_dict)
    
    if not validate_ocr_config(config):
        logger.warning("Invalid custom config, using default")
        return DEFAULT_OCR_CONFIG
    
    return config


# Environment-based configuration loading
def load_config_from_env() -> OCRConfig:
    """Load OCR configuration from environment variables"""
    try:
        config = OCRConfig.from_env()
        if validate_ocr_config(config):
            return config
        else:
            logger.warning("Invalid environment config, using default")
            return DEFAULT_OCR_CONFIG
    except Exception as e:
        logger.error(f"Error loading config from environment: {str(e)}")
        return DEFAULT_OCR_CONFIG
