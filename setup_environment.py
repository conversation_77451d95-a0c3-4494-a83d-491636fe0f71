#!/usr/bin/env python3
"""
LLM-Kit 项目环境配置脚本
自动配置Python依赖、MongoDB数据库等
"""

import os
import sys
import subprocess
import platform
import json
import time
import urllib.request
import zipfile
import shutil
from pathlib import Path

class EnvironmentSetup:
    def __init__(self):
        self.system = platform.system().lower()
        self.project_root = Path(__file__).parent
        self.mongodb_version = "7.0.15"
        self.mongodb_port = 27017
        self.mongodb_data_dir = self.project_root / "mongodb_data"
        self.mongodb_log_dir = self.project_root / "mongodb_logs"
        
    def run_command(self, command, check=True, shell=True):
        """执行命令并返回结果"""
        try:
            print(f"执行命令: {command}")
            result = subprocess.run(
                command, 
                shell=shell, 
                check=check, 
                capture_output=True, 
                text=True,
                encoding='utf-8'
            )
            if result.stdout:
                print(f"输出: {result.stdout}")
            return result
        except subprocess.CalledProcessError as e:
            print(f"命令执行失败: {e}")
            if e.stderr:
                print(f"错误: {e.stderr}")
            if check:
                raise
            return e
    
    def check_python_version(self):
        """检查Python版本"""
        print("\n=== 检查Python版本 ===")
        version = sys.version_info
        print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
        
        if version.major != 3 or version.minor < 8:
            raise RuntimeError("需要Python 3.8或更高版本")
        
        print("✅ Python版本检查通过")
    
    def check_gpu_support(self):
        """检查GPU支持"""
        print("\n=== 检查GPU支持 ===")
        try:
            result = self.run_command("nvidia-smi", check=False)
            if result.returncode == 0:
                print("✅ 检测到NVIDIA GPU")
                return True
            else:
                print("⚠️ 未检测到NVIDIA GPU，将使用CPU模式")
                return False
        except:
            print("⚠️ 未检测到NVIDIA GPU，将使用CPU模式")
            return False
    
    def install_python_dependencies(self):
        """安装Python依赖包"""
        print("\n=== 安装Python依赖包 ===")
        
        # 升级pip
        self.run_command(f"{sys.executable} -m pip install --upgrade pip")
        
        # 安装PyTorch (根据GPU支持选择版本)
        gpu_available = self.check_gpu_support()
        
        if gpu_available:
            print("安装GPU版本的PyTorch...")
            torch_command = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121"
        else:
            print("安装CPU版本的PyTorch...")
            torch_command = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
        
        self.run_command(torch_command)
        
        # 安装其他依赖
        print("安装项目依赖...")
        requirements_file = self.project_root / "requirements.txt"
        if requirements_file.exists():
            self.run_command(f"{sys.executable} -m pip install -r {requirements_file}")
        else:
            print("requirements.txt不存在，手动安装核心依赖...")
            core_packages = [
                "fastapi", "uvicorn", "motor", "pymongo", "pydantic", "pydantic-settings",
                "transformers>=4.46.3", "accelerate>=0.26.0", "sentencepiece", "protobuf",
                "pillow", "numpy", "pandas", "tiktoken", "pyyaml", "tqdm",
                "PyMuPDF", "loguru", "python-multipart", "requests",
                "jieba", "datasketch", "levenshtein", "bcrypt", "sqlalchemy",
                "pynvml", "nvidia-ml-py"
            ]
            
            for package in core_packages:
                try:
                    self.run_command(f"{sys.executable} -m pip install {package}")
                except:
                    print(f"⚠️ 安装 {package} 失败，跳过...")
        
        print("✅ Python依赖安装完成")
    
    def download_mongodb(self):
        """下载MongoDB"""
        print(f"\n=== 下载MongoDB {self.mongodb_version} ===")
        
        if self.system == "windows":
            mongodb_url = f"https://fastdl.mongodb.org/windows/mongodb-windows-x86_64-{self.mongodb_version}.zip"
            mongodb_filename = f"mongodb-windows-x86_64-{self.mongodb_version}.zip"
        elif self.system == "linux":
            mongodb_url = f"https://fastdl.mongodb.org/linux/mongodb-linux-x86_64-ubuntu2004-{self.mongodb_version}.tgz"
            mongodb_filename = f"mongodb-linux-x86_64-ubuntu2004-{self.mongodb_version}.tgz"
        elif self.system == "darwin":  # macOS
            mongodb_url = f"https://fastdl.mongodb.org/osx/mongodb-macos-x86_64-{self.mongodb_version}.tgz"
            mongodb_filename = f"mongodb-macos-x86_64-{self.mongodb_version}.tgz"
        else:
            raise RuntimeError(f"不支持的操作系统: {self.system}")
        
        mongodb_dir = self.project_root / "mongodb"
        mongodb_file = self.project_root / mongodb_filename
        
        # 检查是否已经下载
        if mongodb_dir.exists() and (mongodb_dir / "bin").exists():
            print("✅ MongoDB已存在，跳过下载")
            return mongodb_dir
        
        print(f"从 {mongodb_url} 下载MongoDB...")
        try:
            urllib.request.urlretrieve(mongodb_url, mongodb_file)
            print("✅ MongoDB下载完成")
        except Exception as e:
            print(f"❌ MongoDB下载失败: {e}")
            print("请手动下载MongoDB并解压到项目目录")
            return None
        
        # 解压MongoDB
        print("解压MongoDB...")
        try:
            if mongodb_filename.endswith('.zip'):
                with zipfile.ZipFile(mongodb_file, 'r') as zip_ref:
                    zip_ref.extractall(self.project_root)
            else:
                self.run_command(f"tar -xzf {mongodb_file} -C {self.project_root}")
            
            # 重命名解压后的目录
            extracted_dirs = [d for d in self.project_root.iterdir() 
                            if d.is_dir() and d.name.startswith('mongodb-')]
            if extracted_dirs:
                extracted_dirs[0].rename(mongodb_dir)
            
            # 删除下载的压缩文件
            mongodb_file.unlink()
            print("✅ MongoDB解压完成")
            return mongodb_dir
            
        except Exception as e:
            print(f"❌ MongoDB解压失败: {e}")
            return None
    
    def setup_mongodb(self):
        """配置MongoDB"""
        print("\n=== 配置MongoDB ===")
        
        # 下载MongoDB
        mongodb_dir = self.download_mongodb()
        if not mongodb_dir:
            print("❌ MongoDB配置失败")
            return False
        
        # 创建数据和日志目录
        self.mongodb_data_dir.mkdir(exist_ok=True)
        self.mongodb_log_dir.mkdir(exist_ok=True)
        
        # 创建MongoDB配置文件
        config_content = f"""
# MongoDB配置文件
storage:
  dbPath: {self.mongodb_data_dir.absolute()}
  journal:
    enabled: true

systemLog:
  destination: file
  logAppend: true
  path: {self.mongodb_log_dir.absolute()}/mongod.log

net:
  port: {self.mongodb_port}
  bindIp: 127.0.0.1

processManagement:
  fork: false
"""
        
        config_file = self.project_root / "mongod.conf"
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print("✅ MongoDB配置完成")
        return True
    
    def create_startup_scripts(self):
        """创建启动脚本"""
        print("\n=== 创建启动脚本 ===")
        
        # MongoDB启动脚本
        if self.system == "windows":
            mongodb_start_script = f"""@echo off
echo 启动MongoDB...
cd /d "{self.project_root}"
mongodb\\bin\\mongod.exe --config mongod.conf
pause
"""
            script_file = self.project_root / "start_mongodb.bat"
        else:
            mongodb_start_script = f"""#!/bin/bash
echo "启动MongoDB..."
cd "{self.project_root}"
./mongodb/bin/mongod --config mongod.conf
"""
            script_file = self.project_root / "start_mongodb.sh"
            
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(mongodb_start_script)
        
        if self.system != "windows":
            os.chmod(script_file, 0o755)
        
        # 应用启动脚本
        if self.system == "windows":
            app_start_script = f"""@echo off
echo 启动LLM-Kit应用...
cd /d "{self.project_root}"
python main.py
pause
"""
            app_script_file = self.project_root / "start_app.bat"
        else:
            app_start_script = f"""#!/bin/bash
echo "启动LLM-Kit应用..."
cd "{self.project_root}"
python main.py
"""
            app_script_file = self.project_root / "start_app.sh"
            
        with open(app_script_file, 'w', encoding='utf-8') as f:
            f.write(app_start_script)
        
        if self.system != "windows":
            os.chmod(app_script_file, 0o755)
        
        print("✅ 启动脚本创建完成")
    
    def create_env_file(self):
        """创建环境变量文件"""
        print("\n=== 创建环境变量文件 ===")
        
        env_content = f"""# LLM-Kit 环境变量配置

# MongoDB配置
MONGODB_URL=mongodb://localhost:{self.mongodb_port}
DATABASE_NAME=llm_kit

# OCR配置
OCR_MODEL_NAME=base
OCR_USE_TROCR=true
OCR_BATCH_SIZE=4
OCR_CACHE_DIR=./models_cache

# 应用配置
APP_HOST=127.0.0.1
APP_PORT=8000
LOG_LEVEL=INFO

# GPU配置 (如果有GPU)
CUDA_VISIBLE_DEVICES=0
"""
        
        env_file = self.project_root / ".env"
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print("✅ 环境变量文件创建完成")
    
    def test_installation(self):
        """测试安装"""
        print("\n=== 测试安装 ===")
        
        # 测试Python包导入
        test_packages = [
            "fastapi", "uvicorn", "motor", "pymongo", "transformers",
            "torch", "PIL", "numpy", "pandas"
        ]
        
        for package in test_packages:
            try:
                __import__(package)
                print(f"✅ {package} 导入成功")
            except ImportError as e:
                print(f"❌ {package} 导入失败: {e}")
        
        print("✅ 安装测试完成")
    
    def run_setup(self):
        """运行完整的环境配置"""
        print("🚀 开始配置LLM-Kit项目环境...")
        
        try:
            self.check_python_version()
            self.install_python_dependencies()
            self.setup_mongodb()
            self.create_startup_scripts()
            self.create_env_file()
            self.test_installation()
            
            print("\n🎉 环境配置完成！")
            print("\n📋 下一步操作:")
            print("1. 启动MongoDB:")
            if self.system == "windows":
                print("   双击运行 start_mongodb.bat")
            else:
                print("   ./start_mongodb.sh")
            
            print("2. 启动应用:")
            if self.system == "windows":
                print("   双击运行 start_app.bat")
            else:
                print("   ./start_app.sh")
            
            print("3. 访问应用: http://127.0.0.1:8000")
            print("4. 查看API文档: http://127.0.0.1:8000/docs")
            
        except Exception as e:
            print(f"\n❌ 环境配置失败: {e}")
            sys.exit(1)

if __name__ == "__main__":
    setup = EnvironmentSetup()
    setup.run_setup()
