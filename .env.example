# LLM-Kit 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# ===========================================
# 应用基础配置
# ===========================================
APP_HOST=127.0.0.1
APP_PORT=8000
APP_DEBUG=false
LOG_LEVEL=INFO

# ===========================================
# MongoDB 数据库配置
# ===========================================
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=llm_kit

# MongoDB 连接池配置
MONGODB_MAX_POOL_SIZE=100
MONGODB_MIN_POOL_SIZE=10
MONGODB_MAX_IDLE_TIME_MS=30000
MONGODB_CONNECT_TIMEOUT_MS=10000
MONGODB_SERVER_SELECTION_TIMEOUT_MS=5000

# ===========================================
# OCR 配置
# ===========================================
# TrOCR 模型配置
OCR_MODEL_NAME=base
OCR_USE_TROCR=true
OCR_DEVICE=auto
OCR_CACHE_DIR=./models_cache

# OCR 处理参数
OCR_BATCH_SIZE=4
OCR_MAX_LENGTH=512
OCR_NUM_BEAMS=4
OCR_EARLY_STOPPING=true

# OCR 图像预处理
OCR_RESIZE_IMAGES=true
OCR_TARGET_HEIGHT=384
OCR_ENHANCE_CONTRAST=false

# OCR 性能配置
OCR_ENABLE_GPU=true
OCR_LOW_MEMORY_MODE=false
OCR_FALLBACK_TO_GOT_OCR=true
OCR_RETRY_ATTEMPTS=3

# ===========================================
# LLM 模型配置
# ===========================================
# 默认模型设置
DEFAULT_MODEL_NAME=erine
DEFAULT_PARALLEL_NUM=5
DEFAULT_MAX_TOKENS=2048

# 模型 API 密钥 (请填入实际的密钥)
# 百度文心一言
ERNIE_API_KEY=your_ernie_api_key_here
ERNIE_SECRET_KEY=your_ernie_secret_key_here

# 阿里通义千问
QWEN_API_KEY=your_qwen_api_key_here

# 其他模型 API 密钥
FLASH_API_KEY=your_flash_api_key_here
LITE_API_KEY=your_lite_api_key_here

# ===========================================
# 文件处理配置
# ===========================================
# 上传文件配置
MAX_FILE_SIZE=104857600  # 100MB
UPLOAD_DIR=./uploads
TEMP_DIR=./temp
OUTPUT_DIR=./outputs

# 支持的文件类型
SUPPORTED_FILE_TYPES=pdf,txt,png,jpg,jpeg,bmp,tiff,tex,json

# 文件处理参数
MAX_CHUNK_SIZE=4096
CHUNK_OVERLAP=200
PDF_DPI=300

# ===========================================
# QA 生成配置
# ===========================================
# QA 生成参数
QA_MIN_QUESTION_LENGTH=10
QA_MAX_QUESTION_LENGTH=200
QA_MIN_ANSWER_LENGTH=20
QA_MAX_ANSWER_LENGTH=500

# 质量控制参数
QUALITY_SIMILARITY_THRESHOLD=0.7
QUALITY_MIN_SCORE=0.6
QUALITY_MAX_ATTEMPTS=3

# ===========================================
# 去重配置
# ===========================================
# MinHash 去重参数
DEDUP_NUM_PERM=128
DEDUP_THRESHOLD=0.8
DEDUP_BY_ANSWER=false
MIN_ANSWER_LENGTH=10

# LSH 配置
LSH_NUM_BANDS=16
LSH_ROWS_PER_BAND=8

# ===========================================
# 缓存配置
# ===========================================
# Redis 配置 (可选)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=20

# 本地缓存配置
CACHE_DIR=./cache
CACHE_MAX_SIZE=1073741824  # 1GB
CACHE_TTL=3600  # 1小时

# ===========================================
# 安全配置
# ===========================================
# JWT 配置 (如果需要认证)
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440  # 24小时

# CORS 配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOW_HEADERS=*

# ===========================================
# GPU 配置
# ===========================================
# CUDA 配置
CUDA_VISIBLE_DEVICES=0
CUDA_MEMORY_FRACTION=0.8

# PyTorch 配置
TORCH_HOME=./torch_cache
TRANSFORMERS_CACHE=./transformers_cache
HF_HOME=./huggingface_cache

# ===========================================
# 监控和日志配置
# ===========================================
# 日志配置
LOG_DIR=./logs
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# 性能监控
ENABLE_METRICS=true
METRICS_PORT=9090

# 错误追踪
SENTRY_DSN=your_sentry_dsn_here

# ===========================================
# 开发配置
# ===========================================
# 开发模式配置
DEVELOPMENT_MODE=false
AUTO_RELOAD=false
DEBUG_SQL=false

# 测试配置
TEST_DATABASE_NAME=llm_kit_test
TEST_MONGODB_URL=mongodb://localhost:27017

# ===========================================
# 第三方服务配置
# ===========================================
# ModelScope 配置
MODELSCOPE_CACHE=./modelscope_cache
MODELSCOPE_API_TOKEN=your_modelscope_token_here

# Hugging Face 配置
HF_TOKEN=your_huggingface_token_here
HF_ENDPOINT=https://huggingface.co

# ===========================================
# 其他配置
# ===========================================
# 时区配置
TIMEZONE=Asia/Shanghai

# 语言配置
DEFAULT_LANGUAGE=zh-CN
SUPPORTED_LANGUAGES=zh-CN,en-US

# 分页配置
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100
