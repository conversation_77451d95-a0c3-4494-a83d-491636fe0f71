export default {
  root: {
    title: "LLM-Kit"
  },
  nav: {
    home: "Home",
    data: "Data Management",
    construct: "Dataset Construction",
    quality: "Quality Evaluation",
    dedup: "Deduplication"
  },
  home: {
    hero: {
      title: "LLM-Kit: Knowledge Iteration Kit for LLM",
      subtitle: "Advanced data iteration optimization tool for knowledge refinement and LLM development",
      get_started: "Get Started"
    },
    features: {
      heading: "Core Features",
      data: {
        title: "Data Management",
        description: "Create and manage data pools, upload datasets, perform operations"
      },
      construct: {
        title: "Dataset Construction",
        description: "Build high-quality training datasets through various methods"
      },
      quality: {
        title: "Quality Evaluation",
        description: "Evaluate dataset quality to ensure training effectiveness"
      },
      dedup: {
        title: "Data Deduplication",
        description: "Identify and remove duplicate data to improve training efficiency"
      },
      deploy: {
        title: "Deployment Management",
        description: "Simplify model deployment process and optimize inference performance"
      },
      config: {
        title: "System Settings",
        description: "Configure system parameters and manage model lists"
      }
    },
    about: {
      title: "About LLM-Kit",
      description: "LLM-Kit is a knowledge iteration toolkit designed to optimize large language model data processing. It helps you refine and improve your datasets through iterative cycles, enhancing model performance at each step from data collection to evaluation and deployment."
    }
  },
  sidebar: {
    data_manager: "Data Manage",
    dataset_construct: "Dataset Construction",
    quality_eval: "Quality Evaluation",
    deduplication: "Deduplication",
    deployment_manager: "Deployment",
    record: "Record Management",
    settings: "Settings",
  },
  components: {
    model_card: {
      adapters: "Adapters",
      id: "ID",
      name_and_description: "Name & Description",
      action: "Action",
    },
    go_back: "Go back",
    visibility_button: {
      hide: "Private",
      publicize: "Public",
    },
    data: {
      data_pool_selector: "data pool:",
      data_pool_des: "select a data pool",
      data_set_selector: "data set:"
    },
    eval_metrics_description: {
      acc_des: "Number of correctly predicted samples/total number of samples",
      recall_des: "Judging the similarity between two sentences based on recall rate",
      f1score_des: "Harmony between Accuracy and Recall",
      pre_des: "Evaluate the proportion of content that matches the reference text in the generated text",
      bleu_des: "Judging the similarity between two sentences based on accuracy",
      distinct_des: "Reflecting the diversity of text generation"
    },
    device: {
      // GPU_utilization: "GPU utilization:",
      // memory_utilization: "Memory utilization:",
      GPU_utilization: "GPU Utilization:",
      memory_utilization: "GPU Memory:",
    },
    deployment_params: {
      title: "Deployment Parameters",
      subtitle: "Quantization Parameters",
      subsubtitle: "Quantization Deployment Parameters",
      bits_and_bytes: "Whether to use bits_and_bytes",
      use_flash_attention: "Whether to use flash attention",
      use_deepspeed: "Whether to use deepspeed",
      use_vllm: "Whether to use vllm",
      description: "Deployment Parameters"
    }
  },
  data: {
    title: "Data Management",
    description: "Manage and organize your data",
    upload: {
      title: "Data Upload",
    },
    preview: "Preview",
    download: "Download",
    create_pool: "Create Data Pool",
    no_dataset: "No dataset in the data pool",
    detail: {
      title: "Details",
      detail: "Data Pool Details",
      delete: "Delete this data pool",
      filter: "Selecting",
      create_on: "Creation Time:",
      size: "Data Volume:",
      title_1: "Confirm Deletion?",
      p1: "Are you sure you want to delete it?",
      p2: "Data will",
      p3: "not",
      p4: "be recoverable.",
      yes: "Yes",
      no: "No",
      title_2: "There are still unsubmitted data in the staging area",
      p5: "Are you sure you want to go back? There are still unsubmitted data in the staging area.",
      p6: "The data in the staging area will",
      p7: "not",
      p8: "be saved.",
    },
    table: {
      col_name: "Name",
      col_time: "Time",
      col_size: "Size",
      col_format: "Format",
      col_des: "Description",
      col_operation: "Operation"
    },
    delete: {
      title: "Confirm Deletion",
      data: "Delete Data",
      p1: "Are you sure you want to delete this dataset?",
      p2: "The data will not be recoverable after deletion.",
      yes: "Delete",
      no: "No"
    },
    construct: {
      title: "title",
      uploaded_files:"A list of resolved files",
      type:"type",
      size:"size",
      deselect_all:'deselect_all',
      select_all:'select_all',
      erine:'erine',
      flash:'flash',
      qwen:'qwen',
      content:'content',
      reasoning_process:'reasoning_process',
      lite:'lite',
      cot_generating:'cot_generating',
      qa_delete_success:"qa_delete_success",
      latex_converting:'latex_converting',
      qa_generating:"qa_generating",
      qa_generating_wait:'qa_generating_wait',
      raw_file_preview_for_file:'filename:',
      upload_status:'status(Click to preview)',
      filename:'filename(Click to preview)',
      qa_generation_settings:"Parameter Input",
      main_settings:"Q&A pairs are generated",
      cotgenerate:'COT Q&A pair generation',
      raw_file_preview_title:'raw_file_preview',
      delete_qa_button:'delete_qas',
      status_generated:"Constructed,",
      status_generating:'unconstructed',
      delete_button:'delete',
      delete_confirmation_title:"Confirm the deletion",
      delete_confirmation_message:'Whether to delete or not',
      delete_confirm_button:'Confirm',
      delete_cancel_button:'Cancel',
      modification_time:'Creation time',
      parallel_num:'Number of parallels',
      sk:"s key",
      ak:'api key',
      qa_preview_title: "QA Preview",
      cot_preview_title:'COT Preview',
      no_reasoning_available:'no_reasoning_available',
      qa_preview_for_file: "Original file name:",
      question: "Question",
      answer: "Answer",
      text_context: "Context Text",
      previous_page: "Previous",
      next_page: "Next",
      page: "Page",
      go_to_page: "Go to page:",
      preview_qa_fetch_failed: "Failed to fetch QA content for preview",
      preview_qa_network_error: "Network error fetching QA content",
      no_qa_content_available: "No QA content available for this file.",
      preview_cot_fetch_failed: "Failed to fetch COT content for preview",
      preview_cot_network_error: "Network error fetching COT content",
      no_cot_content_available: "No COT content available for this file.",
      save_path:'save_name',
      save_path_placeholder:"Default original file name",
      domain:'Dataset domain information',
      generate_button:'generate',
      p1: "Original Dataset",
      AK: "API KEY：",
      AU: "API URL：",
      prompt: "prompt：",
      name: "name：",
      des: "des：",
      begin: "begin",
      zone: "zone",
      model_select:"model_select",
      no_file: "no_file",
      submit: "submit",
      model_name: "model_name：",
      progress: "progress：",
      remain_time: "remain_time：",
      constructing: "constructing",
      construct_finish: "construct_finish！",
      wait: "wait",
      invalid_sk_ak:"Invalid SK and AK",
      subtitle: "subtitle",
      create_task: "create_task",
      next_step: "next_step",
      previous_step: "previous_step",
    },
    uploader: {
      fetch_fail:'file_fetch_fail',
      title:'dataset_manage',
      col_filename: "Filename",
      upload_status:'status' ,
      parsed:'parsed',
      pending:'unparse',
      parse_button:"parse",
      action:'action',
      delete_action:'delete_action',
      delete_button: 'delete',
      col_datasetname: "Dataset Name",
      col_des: "Description",
      filename:"filename",
      file_type:"file_type",
      parse_status:"parse_status",
      size:"file_size",
      created_at:"Upload time",
      failed:"end",
      uploaded_files:"Uploaded Files",
      col_option: "Operation",
      datapool_detail: "Data Pool Details",
      zone: "Staging Area",
      format: "Format",
      submit: "Submit all files in the staging area",
      no_file: "No uploaded files in the staging area",
      enter_name: "Enter dataset name",
      enter_des: "Enter dataset description",
      move: "Move out of staging area",
      click: "Click ",
      or: "or",
      p1: " Drag and drop ",
      p2: "to upload files to the staging area"
    },
    task: {
      steps: {
        infor: "Basic Information",
        upload: "Upload Data",
        infor_des: "Fill in the basic information of the created data pool",
        upload_des: "Select the data to be uploaded"
      },
      p1: "Are you sure you have finished creating? There are still unsubmitted data in the staging area.",
      p2: "The data in the staging area will",
      p3: "not",
      p4: "be saved.",
      yes: "Yes",
      no: "No",
      title: "Create Data Pool",
      description: "Create a data pool according to the prompted steps",
      complete: "Complete",
      name: "Data Pool Name",
      enter_name: "Please enter the data pool name",
      des: "Data Pool Description",
      enter_des: "Please enter the data pool description"
    },
    filter: {
      title: "Data Selection",
      p1: "Original Dataset:",
      p2: "Retention Ratio:",
      name: "New Dataset Name:",
      des: "New Dataset Description:",
      begin: "Start Selecting"
    }
  },
  fault: {
    title: "Error log",
    description: "View logs of errors in tasks and processes",
    message: "Message",
    source: "Source",
    time: "Time",
    code: "Code",
    action: "Action",
    view_logs: "View logs",
    download_logs: "Download logs",
    search_placeholder: "Enter the tags separated by commas",
    search: "Search",
    management: "Fine-tuning Management",
    finetune: "Fine-tuning",
    create_task: "Create Fine-tuning Task",
    wordcloud: "WordCloud",
    close: "close"
  },
  config: {
    log_out: "Log out",
    model_list: "Model list",
    title: "Settings",
    description: "Configure system parameters"
  },
  deduplication: {
    title: "Deduplication",
    select:  "Select",
    description: "Deduplicate the dataset to ensure the quality of the dataset",
    min_answer_length: "Min Answer Length:",
    dedup_by_answer: "Deduplicated by Answer:",
    dedup_threshold: "Deduplication Threshold:",
    action: "Action",
    close: "close",
    params: "Parameters",
    begin: "Deduplicate Start"
  },
  quality_eval: {
    title: "Quality Control",
    subtitle: "Improve the quality of your datasets",
    start: "Start Quality Control",
    model_name: "Model Selection:",
    name: "Input Filename:",
    domain: "New Dataset Description:",
    optional: "(Optional)",
    max_attempts: "Maximum attempts:",
    similarity_rate: "Similarity Rate:",
    coverage_rate: "Coverage Rate:",
    params: "Paramters",
    parallel_num: "Parallel Number:",
    AK: "API Key",
    SK: "Secret Key",
    qa_files: "QA Pairs Files",
    files: {
      record_id: "ID",
      filename: "Filename",
      create_at: "Create Time",
      select: "Select"
    },
    progress: {
      progress: "Progress",
      processing: "Processing",
      completed: "Finish",
      failed: "Failed",
      timeout: "Timeout",
      error: "Error",
      not_found: "Not Found",
      remain_time: "Remain Time:",
      error_info: "Error Information:",
      wait: "Wait",
    }
  },
  construct: {
    title: "Dataset Construction",
    subtitle: "Construct a question-answer pair data set",
    create_task: "Create Construction Task",
    next_step: "Next Step",
    previous_step: "Previous Step",
    main_settings: "Construction Settings",
    uploaded_files: "Parsed Files",
    filename: "Filename",
    upload_status: "Status",
    select_all: "Select All",
    deselect_all: "Deselect All",
    status_generated: "Generated",
    status_generating: "Generating...",
    status_unknown: "Not Generated",
    delete_qa_button: "Delete QA",
    delete_cot_button: "Delete COT",
    delete_button: "Delete Selected",
    latex_converting: "Converting LaTeX...",
    qa_generating: "Generating QA Pairs...",
    qa_generated_success: "QA Generation Complete",
    qa_generation_failed: "QA Generation Failed",
    qa_generation_network_error: "Network Error During QA Generation",
    latex_conversion_failed: "LaTeX Conversion Failed",
    latex_conversion_network_error: "Network Error During LaTeX Conversion",
    qa_generation_settings: "QA Generation Settings",
    cot_generation_settings: "CoT Generation Settings",
    parallel_num: "Parallel Processing Threads",
    save_path: "Save Path",
    save_path_placeholder: "Path to save generated files",
    model_name: "Model",
    erine: "ERNIE",
    flash: "Flash",
    lite: "Lite",
    qwen: "Qwen",
    sk: "Secret Key",
    ak: "Access Key",
    domain: "Domain",
    domain_placeholder: "Enter domain (optional)",
    generate_qa_button: "Generate QA Pairs",
    generating_qa: "Generating QA Pairs...",
    qa_delete_success: "QA file deleted successfully",
    qa_delete_failed: "Failed to delete QA file",
    qa_delete_network_error: "Network error deleting QA file",
    qa_preview_failed: "Failed to preview QA file",
    qa_preview_network_error: "Network error previewing QA file",
    generate_cot_button: "Generate CoT Data",
    generating_cot: "Generating CoT Data...",
    cot_delete_success: "CoT file deleted successfully",
    cot_delete_failed: "Failed to delete CoT file",
    cot_delete_network_error: "Network error deleting CoT file",
    cot_preview_failed: "Failed to preview CoT file",
    cot_preview_network_error: "Network error previewing CoT file",
    delete_confirmation_title: "Confirm Deletion",
    delete_confirmation_message: "Are you sure you want to delete the selected files? This action cannot be undone.",
    delete_confirm_button: "Delete",
    delete_cancel_button: "Cancel",
    qa_preview_title: "QA Preview",
    cot_preview_title: "COT Preview",
    no_reasoning_available: "No Results Available",
    qa_preview_for_file: "Original file name:",
    question: "Question",
    answer: "Answer",
    text_context: "Context Text",
    previous_page: "Previous",
    next_page: "Next",
    page: "Page",
    go_to_page: "Go to page:",
    preview_qa_fetch_failed: "Failed to fetch QA content for preview",
    preview_qa_network_error: "Network error fetching QA content",
    no_qa_content_available: "No QA content available for this file.",
    preview_cot_fetch_failed: "Failed to fetch COT content for preview",
    preview_cot_network_error: "Network error fetching COT content",
    no_cot_content_available: "No COT content available for this file.",
    invalid_sk_ak: "Invalid Secret Key and Access Key",
    qa_preview: "QA Preview",
    cot_preview: "COT Preview",
    file_preview: "File:",
    raw_file_preview_title: "File Preview",
    content: "Content",
    reasoning: "Reasoning",
    context: "Context",
    previous: "Previous",
    next: "Next",
    go_to: "Go to page:",
    no_content: "No content available",
    no_reasoning: "No reasoning available",
    preview_failed: "Failed to fetch content",
    network_error: "Network error fetching content",
    no_file_selected: "No file selected",
    preview_raw_fetch_failed: "Failed to fetch file content",
    preview_raw_network_error: "Network error fetching file content",
    raw_file_preview_for_file: "File:",
    no_raw_content_available: "No content available for this file",
    cot_generating: "Generating CoT Data...",
    cot_generated_success: "CoT Generation Complete",
    cot_generation_failed: "CoT Generation Failed",
    cot_generation_network_error: "Network Error During CoT Generation",
    cot_all_generated_success: "All CoT Generation Complete"
  },
  record: {
    title: "Record Management",
    description: "Manage all task records.",
    search: "Search",
    view_logs: "View Logs",
    message: "Message",
    time: "Time",
    source: "Source",
    action: "Action",
    tasks: {
      parse: "Parse",
      to_tex: "Convert to TeX",
      generate_qa: "Generate QA"
    }
  }
};
