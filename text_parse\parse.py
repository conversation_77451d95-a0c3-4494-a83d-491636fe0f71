import os
import logging
from PIL import Image

from utils.hparams import HyperParams
from .ocr_service import get_ocr_service
from .ocr_config import get_ocr_config

# Configure logging
logger = logging.getLogger(__name__)

def single_ocr(image_input, model_name='base', use_trocr=True):
    """
    Perform OCR on a single image using TrOCR or fallback to GOT-OCR2_0

    Args:
        image_input: Image file path, PIL Image, or numpy array
        model_name: TrOCR model variant ('base', 'large', 'handwritten', 'stage1', 'str')
        use_trocr: Whether to use TrOCR (True) or fallback to GOT-OCR2_0 (False)

    Returns:
        Recognized text string
    """
    try:
        # Get OCR configuration based on model name
        if model_name == 'handwritten':
            config = get_ocr_config('handwritten_text')
        elif model_name == 'large':
            config = get_ocr_config('high_quality')
        elif model_name == 'str':
            config = get_ocr_config('scene_text')
        else:
            config = get_ocr_config('default')

        # Override model name and TrOCR usage
        config.model_name = model_name
        config.use_trocr = use_trocr

        # Get OCR service and perform recognition
        ocr_service = get_ocr_service(config)
        result = ocr_service.recognize_single(image_input)

        return result

    except Exception as e:
        logger.error(f"OCR failed: {str(e)}")
        return ""


def batch_ocr(images, model_name='base', use_trocr=True, progress_callback=None):
    """
    Perform OCR on multiple images using TrOCR

    Args:
        images: List of image inputs (file paths, PIL Images, or numpy arrays)
        model_name: TrOCR model variant
        use_trocr: Whether to use TrOCR
        progress_callback: Optional progress callback function

    Returns:
        List of recognized text strings
    """
    try:
        # Get OCR configuration
        if model_name == 'handwritten':
            config = get_ocr_config('handwritten_text')
        elif model_name == 'large':
            config = get_ocr_config('high_quality')
        elif model_name == 'str':
            config = get_ocr_config('scene_text')
        else:
            config = get_ocr_config('default')

        config.model_name = model_name
        config.use_trocr = use_trocr

        # Get OCR service and perform batch recognition
        ocr_service = get_ocr_service(config)
        results = ocr_service.recognize_batch(images, progress_callback=progress_callback)

        return results

    except Exception as e:
        logger.error(f"Batch OCR failed: {str(e)}")
        return [""] * len(images)

def text_parse(file_path,save_path):
    """
    Text document parsing, which is essentially making a copy of the file
    :param file_path: Original file path
    :param save_path: Path for the parsed file
    :return: Path of the parsed file
    """

    # Read file content
    with open(file_path, 'r', encoding='utf-8') as f:
        data = f.readlines()

    print(f"Parsing text file and saving results to {save_path}")

    # Get filename and construct save path
    file_name = file_path.split('/')[-1]  # Extract filename
    base_name = file_name.split('.')[0]  # Extract part without extension
    save_file_path = os.path.join(save_path, base_name + '.txt').replace('\\', '/') # Concatenate full path

    # Ensure the save path directory exists
    os.makedirs(save_path, exist_ok=True)
    # Write to file
    with open(save_file_path, 'w', encoding='utf-8') as f:
        for line in data:
            f.write(line)
    return save_file_path

def pdf_parse(file_path, save_path, progress_callback=None):
    """PDF file parsing, supports progress callback"""
    import fitz

    # Open PDF file
    pdf_document = fitz.open(file_path)
    total_pages = pdf_document.page_count

    # Check if it's a scanned image PDF
    is_scanned = True
    for page in pdf_document:
        if page.get_text().strip():
            is_scanned = False
            break

    results = []
    if is_scanned:
        for page_num in range(total_pages):
     
            page = pdf_document[page_num]
            pix = page.get_pixmap()
            img_data = pix.tobytes()
            img = Image.frombytes("RGB", [pix.width, pix.height], img_data)

            text = single_ocr(img)
            results.append(text)

            if progress_callback:
                # Update progress, reserve 20% for file saving phase
                progress = int((page_num + 1) / total_pages * 80)
                progress_callback(progress)
    else:
        # Process text PDF
        for page_num in range(total_pages):
            page = pdf_document[page_num]
            text = page.get_text()
            results.append(text)

            if progress_callback:
                # Update progress, reserve 20% for file saving phase
                progress = int((page_num + 1) / total_pages * 80)
                progress_callback(progress)

    # Save results
    save_file_path = os.path.join(
        save_path,
        f"{os.path.splitext(os.path.basename(file_path))[0]}.txt"
    )

    with open(save_file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(results))

    if progress_callback:
        progress_callback(100)  # Complete

    pdf_document.close()
    return save_file_path




def parse(hparams: HyperParams, progress_callback=None):
    """
    File parsing function, supports progress callback
    :param hparams: Hyperparameters
    :param progress_callback: Progress callback function
    :return: Path of the parsed file
    """
    assert os.path.exists(hparams.file_path), "File does not exist."
    file_type = hparams.file_path.split('.')[-1].lower()
    save_path = os.path.join(hparams.save_path, 'parsed_file')
    os.makedirs(save_path, exist_ok=True)

    if file_type in {'tex', 'txt', 'json'}:
        # Use file size instead of line count to estimate progress
        CHUNK_SIZE = 1024 * 1024  # 1MB
        file_size = os.path.getsize(hparams.file_path)
        read_bytes = 0
        content = []

        with open(hparams.file_path, 'r', encoding='utf-8') as f:
            while chunk := f.read(CHUNK_SIZE):
                content.append(chunk)
                read_bytes += len(chunk.encode('utf-8'))
                if progress_callback:
                    progress = int((read_bytes/file_size) * 70)
                    progress_callback(min(progress, 70))  # Ensure it doesn't exceed 70%

        # Save processed content
        save_file_path = os.path.join(
            save_path,
            f"{os.path.splitext(os.path.basename(hparams.file_path))[0]}.txt"
        )

        with open(save_file_path, 'w', encoding='utf-8') as f:
            f.writelines(content)

        if progress_callback:
            progress_callback(100)
        return save_file_path

    elif file_type == 'pdf':
        return pdf_parse(hparams.file_path, save_path, progress_callback)
    else:
        raise ValueError(f"Unsupported file type: {file_type}")


