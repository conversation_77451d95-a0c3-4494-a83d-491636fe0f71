@echo off
:: Simple and reliable startup script for LLM-Kit
chcp 65001 >nul 2>&1

echo.
echo ========================================
echo    LLM-Kit Simple Startup Script
echo ========================================
echo.

:: Change to project directory
cd /d "d:\pythonprojs\LLM-Kit"
echo [INFO] Working directory: %cd%
echo.

:: Check if we're in the right directory
if not exist "main.py" (
    echo [ERROR] main.py not found in current directory
    echo [ERROR] Please ensure you're in the LLM-Kit project directory
    echo [ERROR] Current directory: %cd%
    pause
    exit /b 1
)

:: Check Python
echo [CHECK] Testing Python...
python --version
if errorlevel 1 (
    echo [ERROR] Python not found or not working
    pause
    exit /b 1
)
echo [SUCCESS] Python is working
echo.

:: Create directories
echo [SETUP] Creating directories...
if not exist "uploads" mkdir uploads
if not exist "temp" mkdir temp
if not exist "outputs" mkdir outputs
if not exist "logs" mkdir logs
if not exist "cache" mkdir cache
if not exist "models_cache" mkdir models_cache
if not exist "mongodb_data" mkdir mongodb_data
if not exist "mongodb_logs" mkdir mongodb_logs
echo [SUCCESS] Directories created
echo.

:: Test basic imports
echo [CHECK] Testing Python imports...
python -c "import fastapi, uvicorn; print('[SUCCESS] FastAPI and Uvicorn available')"
if errorlevel 1 (
    echo [ERROR] FastAPI or Uvicorn not available
    echo [INFO] Installing missing packages...
    pip install fastapi uvicorn
)

python -c "import motor, pymongo; print('[SUCCESS] MongoDB drivers available')"
if errorlevel 1 (
    echo [ERROR] MongoDB drivers not available
    echo [INFO] Installing missing packages...
    pip install motor pymongo
)
echo.

:: Check MongoDB (optional)
echo [CHECK] Testing MongoDB connection...
python -c "import pymongo; client = pymongo.MongoClient('mongodb://localhost:27017', serverSelectionTimeoutMS=2000); client.admin.command('ping'); print('[SUCCESS] MongoDB is running')" 2>nul
if errorlevel 1 (
    echo [WARNING] MongoDB not running or not accessible
    echo [INFO] You can start MongoDB with: start_mongodb_simple.bat
    echo [INFO] Or continue without MongoDB (some features may not work)
    echo.
)

:: Start application
echo [STARTING] Launching LLM-Kit application...
echo.
echo Access URLs:
echo - Main: http://127.0.0.1:8000
echo - Docs: http://127.0.0.1:8000/docs
echo - OCR:  http://127.0.0.1:8000/ocr/status
echo.
echo Press Ctrl+C to stop
echo ========================================
echo.

python main.py

echo.
echo [STOPPED] Application stopped
pause
