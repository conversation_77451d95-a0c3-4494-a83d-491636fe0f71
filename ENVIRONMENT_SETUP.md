# LLM-Kit 环境配置指南

## 🎯 **环境配置总结**

你的LLM-Kit项目环境已基本配置完成！以下是详细的配置信息和使用指南。

## ✅ **已完成的配置**

### **1. 系统环境**
- ✅ **Python**: 3.10.0 (符合要求)
- ✅ **pip**: 25.1 (最新版本)
- ✅ **GPU**: NVIDIA RTX 3060 (支持CUDA 12.9)
- ✅ **Conda环境**: env02py310 (已激活)

### **2. 核心依赖包**
- ✅ **PyTorch**: 2.3.1+cu121 (GPU版本)
- ✅ **Transformers**: 4.46.3 (支持TrOCR)
- ✅ **FastAPI**: 0.115.12 (Web框架)
- ✅ **Motor**: 3.7.1 (异步MongoDB驱动)
- ✅ **PyMongo**: 4.13.0 (MongoDB驱动)
- ✅ **Accelerate**: 1.7.0 (模型加速)
- ✅ **Loguru**: 已安装 (日志系统)

### **3. 项目目录结构**
```
LLM-Kit/
├── uploads/          # 上传文件目录
├── temp/             # 临时文件目录
├── outputs/          # 输出文件目录
├── logs/             # 日志文件目录
├── cache/            # 缓存目录
├── models_cache/     # 模型缓存目录
├── mongodb_data/     # MongoDB数据目录
├── mongodb_logs/     # MongoDB日志目录
├── .env              # 环境变量配置
├── .env.example      # 环境变量示例
├── mongod.conf       # MongoDB配置文件
└── 启动脚本...
```

### **4. 配置文件**
- ✅ **环境变量**: `.env` 和 `.env.example`
- ✅ **MongoDB配置**: `mongod.conf`
- ✅ **依赖清单**: `requirements.txt` (已更新)
- ✅ **启动脚本**: Windows (.bat) 和 Linux/macOS (.sh)

## 🚀 **启动步骤**

### **方法1: 使用启动脚本 (推荐)**

#### **Windows用户:**
```bash
# 1. 启动MongoDB
双击运行: start_mongodb.bat

# 2. 启动应用 (新开命令行窗口)
双击运行: start_app.bat
```

#### **Linux/macOS用户:**
```bash
# 1. 启动MongoDB
./start_mongodb.sh

# 2. 启动应用 (新开终端)
./start_app.sh
```

### **方法2: 手动启动**

#### **1. 启动MongoDB**
```bash
# Windows
mongodb\bin\mongod.exe --config mongod.conf

# Linux/macOS
./mongodb/bin/mongod --config mongod.conf
```

#### **2. 初始化数据库 (首次运行)**
```bash
python init_database.py
```

#### **3. 启动应用**
```bash
python main.py
```

## 🌐 **访问地址**

启动成功后，可以通过以下地址访问：

- **主应用**: http://127.0.0.1:8000
- **API文档**: http://127.0.0.1:8000/docs
- **交互式API**: http://127.0.0.1:8000/redoc

## 🔧 **功能模块**

### **1. OCR功能 (TrOCR)**
- **单图片OCR**: `POST /ocr/single`
- **批量OCR**: `POST /ocr/batch`
- **PDF OCR**: `POST /ocr/pdf`
- **Base64 OCR**: `POST /ocr/base64`

### **2. 文本处理**
- **文件解析**: `POST /parse/`
- **LaTeX转换**: `POST /to_tex/`
- **QA生成**: `POST /qa/`
- **质量控制**: `POST /quality/`
- **去重处理**: `POST /dedup/`

### **3. 数据管理**
- **数据集管理**: `GET/POST /api/datasets`
- **文件上传**: `POST /api/upload`

## ⚠️ **注意事项**

### **1. MongoDB要求**
- MongoDB必须先启动才能运行应用
- 默认端口: 27017
- 数据库名: llm_kit

### **2. GPU使用**
- 自动检测GPU可用性
- 支持CUDA 12.1
- 可通过环境变量控制: `CUDA_VISIBLE_DEVICES=0`

### **3. 模型下载**
- TrOCR模型会在首次使用时自动下载
- 下载位置: `./models_cache/`
- 需要稳定的网络连接

### **4. API密钥配置**
编辑 `.env` 文件，填入实际的API密钥：
```bash
# 百度文心一言
ERNIE_API_KEY=your_actual_api_key_here
ERNIE_SECRET_KEY=your_actual_secret_key_here

# 阿里通义千问
QWEN_API_KEY=your_actual_api_key_here

# 其他模型API密钥
FLASH_API_KEY=your_actual_api_key_here
LITE_API_KEY=your_actual_api_key_here
```

## 🧪 **测试功能**

### **1. 测试OCR功能**
```bash
python test_trocr_ocr.py
```

### **2. 测试API接口**
```bash
# 检查服务状态
curl http://127.0.0.1:8000/ocr/status

# 查看可用模型
curl http://127.0.0.1:8000/ocr/models
```

### **3. 测试数据库连接**
```bash
python -c "import pymongo; client = pymongo.MongoClient('mongodb://localhost:27017'); client.admin.command('ping'); print('MongoDB连接正常')"
```

## 🔍 **故障排除**

### **常见问题:**

1. **MongoDB连接失败**
   - 确保MongoDB正在运行
   - 检查端口27017是否被占用
   - 查看MongoDB日志: `mongodb_logs/mongod.log`

2. **模型下载失败**
   - 检查网络连接
   - 设置代理或使用镜像源
   - 手动下载模型到缓存目录

3. **GPU内存不足**
   - 减小批处理大小: `OCR_BATCH_SIZE=2`
   - 使用低内存模式: `OCR_LOW_MEMORY_MODE=true`
   - 切换到CPU模式: `OCR_DEVICE=cpu`

4. **依赖包冲突**
   - 重新安装依赖: `pip install -r requirements.txt --force-reinstall`
   - 使用虚拟环境隔离依赖

### **日志查看:**
- **应用日志**: `logs/` 目录
- **MongoDB日志**: `mongodb_logs/mongod.log`
- **错误日志**: 数据库 `error_logs` 集合

## 📚 **进一步配置**

### **1. 性能优化**
- 调整MongoDB缓存大小
- 配置GPU内存分配
- 优化批处理参数

### **2. 安全配置**
- 启用MongoDB认证
- 配置HTTPS
- 设置API访问限制

### **3. 扩展功能**
- 添加更多LLM模型
- 集成更多OCR引擎
- 自定义处理流程

## 🎉 **配置完成**

你的LLM-Kit环境已经配置完成！现在可以：

1. 启动MongoDB和应用
2. 访问Web界面进行操作
3. 使用API接口进行开发
4. 上传文件进行文本处理
5. 生成高质量的QA数据集

如有问题，请查看日志文件或参考故障排除部分。

---

**祝你使用愉快！** 🚀
