# PowerShell startup script for LLM-Kit
# Run with: powershell -ExecutionPolicy Bypass -File start_app.ps1

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    LLM-Kit PowerShell Startup Script" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Change to project directory
$ProjectDir = "d:\pythonprojs\LLM-Kit"
Set-Location $ProjectDir
Write-Host "[INFO] Working directory: $(Get-Location)" -ForegroundColor Green
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "main.py")) {
    Write-Host "[ERROR] main.py not found in current directory" -ForegroundColor Red
    Write-Host "[ERROR] Current directory: $(Get-Location)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check Python
Write-Host "[CHECK] Testing Python..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "[SUCCESS] $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Python not found or not working" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Create directories
Write-Host "[SETUP] Creating directories..." -ForegroundColor Yellow
$directories = @("uploads", "temp", "outputs", "logs", "cache", "models_cache", "mongodb_data", "mongodb_logs")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "[CREATED] $dir" -ForegroundColor Green
    } else {
        Write-Host "[EXISTS] $dir" -ForegroundColor Gray
    }
}
Write-Host ""

# Test basic imports
Write-Host "[CHECK] Testing Python imports..." -ForegroundColor Yellow
try {
    python -c "import fastapi, uvicorn; print('[SUCCESS] FastAPI and Uvicorn available')"
} catch {
    Write-Host "[ERROR] FastAPI or Uvicorn not available" -ForegroundColor Red
    Write-Host "[INFO] Installing missing packages..." -ForegroundColor Yellow
    pip install fastapi uvicorn
}

try {
    python -c "import motor, pymongo; print('[SUCCESS] MongoDB drivers available')"
} catch {
    Write-Host "[ERROR] MongoDB drivers not available" -ForegroundColor Red
    Write-Host "[INFO] Installing missing packages..." -ForegroundColor Yellow
    pip install motor pymongo
}
Write-Host ""

# Check MongoDB (optional)
Write-Host "[CHECK] Testing MongoDB connection..." -ForegroundColor Yellow
try {
    python -c "import pymongo; client = pymongo.MongoClient('mongodb://localhost:27017', serverSelectionTimeoutMS=2000); client.admin.command('ping'); print('[SUCCESS] MongoDB is running')" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[SUCCESS] MongoDB is running" -ForegroundColor Green
    } else {
        throw "MongoDB not accessible"
    }
} catch {
    Write-Host "[WARNING] MongoDB not running or not accessible" -ForegroundColor Yellow
    Write-Host "[INFO] You can start MongoDB with: .\start_mongodb.ps1" -ForegroundColor Cyan
    Write-Host "[INFO] Or continue without MongoDB (some features may not work)" -ForegroundColor Cyan
}
Write-Host ""

# Start application
Write-Host "[STARTING] Launching LLM-Kit application..." -ForegroundColor Green
Write-Host ""
Write-Host "Access URLs:" -ForegroundColor Cyan
Write-Host "- Main: http://127.0.0.1:8000" -ForegroundColor White
Write-Host "- Docs: http://127.0.0.1:8000/docs" -ForegroundColor White
Write-Host "- OCR:  http://127.0.0.1:8000/ocr/status" -ForegroundColor White
Write-Host ""
Write-Host "Press Ctrl+C to stop" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

try {
    python main.py
} catch {
    Write-Host ""
    Write-Host "[ERROR] Application failed to start" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "[STOPPED] Application stopped" -ForegroundColor Yellow
Read-Host "Press Enter to exit"
