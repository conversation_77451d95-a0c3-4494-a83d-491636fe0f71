from fastapi import <PERSON><PERSON><PERSON>, Query, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from app.components.routers import parse, to_tex, qa_generate, quality, qa_dedup, cot_generate, dataset, ocr
from app.components.core.database import init_db, get_database
from datetime import datetime, timezone
import logging
from fastapi.responses import JSONResponse
from app.components.models.schemas import  ErrorLogsListResponse

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

app = FastAPI(title="LLM-Kit API")

# 初始化数据库
@app.on_event("startup")
async def startup_db_client():
    await init_db()

class ErrorLoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)

            if response.status_code >= 400:
                error_message = "HTTP {}: {}".format(
                    response.status_code,
                    getattr(response, 'body', b'').decode('utf-8', 'replace')
                )
                await log_error(
                    error_message=error_message,
                    source=request.url.path,
                    request=request,
                    status_code=response.status_code
                )

            return response

        except HTTPException as exc:

            await log_error(
                error_message=str(exc.detail),
                source=request.url.path,
                request=request,
                status_code=exc.status_code
            )
            raise exc
        except Exception as exc:
            raise exc

app.add_middleware(ErrorLoggingMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

async def log_error(error_message: str, source: str, stack_trace: str = None, request=None, status_code: int = 500):

    db = await get_database()
    error_log = {
        "timestamp": datetime.now(timezone.utc),
        "error_message": error_message,
        "source": source,
        "stack_trace": stack_trace,
        "request_path": str(request.url) if request else None,
        "request_method": request.method if request else None,
        "status_code": status_code,
        "request_headers": dict(request.headers) if request else None,
        "request_query_params": dict(request.query_params) if request else None,
    }

    # Try to get the request body
    if request:
        try:
            body = await request.body()
            if body:
                error_log["request_body"] = body.decode('utf-8', 'replace')
        except Exception:
            pass  # If unable to get the request body, ignore it

    await db.llm_kit.error_logs.insert_one(error_log)

async def clear_all_collections():
    """Clear data from all collections"""
    db = await get_database()
    collections = [
        "parse_records",
        "tex_records",
        "qa_generations",
        "qa_pairs",
        "quality_generations",
        "quality_records",
        "dedup_records",
        "kept_pairs",
        "error_logs",
        "uploaded_files",           # Add text file collection
        "uploaded_binary_files",    # Add binary file collection
        "dataset_entries"           # Add dataset entries collection
    ]

    for collection_name in collections:
        collection = db.llm_kit[collection_name]
        try:
            await collection.delete_many({})
            print(f"Cleared collection: {collection_name}")

            # If it's a file-related collection, also clean up files in the file system
            if collection_name in ["uploaded_files", "uploaded_binary_files"]:
                import shutil
                import os

                # Clean up parsed_files directory
                parsed_files_dir = os.path.join("parsed_files", "parsed_file")
                if os.path.exists(parsed_files_dir):
                    shutil.rmtree(parsed_files_dir)
                    os.makedirs(parsed_files_dir, exist_ok=True)
                    print(f"Cleared directory: {parsed_files_dir}")

        except Exception as e:
            await log_error(str(e), f"clear_collection_{collection_name}")
            print(f"Error clearing collection {collection_name}: {str(e)}")

# Register routes
app.include_router(parse.router, prefix="/parse", tags=["parse"])
app.include_router(to_tex.router, prefix="/to_tex", tags=["to_tex"])
app.include_router(qa_generate.router, prefix="/qa", tags=["qa_generate"])
app.include_router(quality.router, prefix="/quality", tags=["quality"])
app.include_router(qa_dedup.router, prefix="/dedup", tags=["qa_dedup"])
app.include_router(cot_generate.router, prefix="/cot", tags=["cot_generate"])
app.include_router(dataset.router, prefix="/api", tags=["dataset"])
app.include_router(ocr.router, prefix="/ocr", tags=["ocr"])

# Health check endpoint
@app.get("/")
async def root():
    return {"status": "ok", "message": "LLM-Kit API is running"}

@app.post("/clear-data")
async def clear_data():
    """API endpoint to manually clear all data"""
    await clear_all_collections()
    return {"message": "All collections cleared successfully"}

# Modify global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    import traceback
    error_msg = str(exc)
    stack_trace = traceback.format_exc()
    await log_error(
        error_msg,
        request.url.path,
        stack_trace,
        request,
        status_code=500
    )
    logging.error(f"Global exception: {error_msg}\n{stack_trace}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": error_msg,
            "path": str(request.url)
        }
    )

@app.get("/error-logs", response_model=ErrorLogsListResponse)
async def get_error_logs(
    limit: int = Query(default=100, ge=1, le=1000),
    skip: int = Query(default=0, ge=0)
):
    """Get recent error logs

    Args:
        limit: Limit on the number of logs to return
        skip: Number of logs to skip
    """
    db = await get_database()
    cursor = db.llm_kit.error_logs.find() \
        .sort("timestamp", -1) \
        .skip(skip) \
        .limit(limit)

    total = await db.llm_kit.error_logs.count_documents({})

    logs = []
    async for log in cursor:
        log['id'] = str(log['_id'])  # Convert ObjectId to string
        del log['_id']  # Delete the original _id field
        logs.append(log)

    return {
        "total": total,
        "logs": logs
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)



