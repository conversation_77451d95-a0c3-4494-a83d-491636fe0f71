"""
Enhanced OCR Service with TrOCR integration
Provides high-level OCR functionality with configuration management
"""

import os
import logging
import time
from typing import List, Union, Optional, Callable, Dict, Any
from PIL import Image, ImageEnhance
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed

from .trocr_ocr import TrOC<PERSON>ng<PERSON>, get_ocr_engine
from .ocr_config import OCRConfig, get_ocr_config

logger = logging.getLogger(__name__)

class OCRService:
    """
    Enhanced OCR Service with TrOCR integration
    Provides high-level OCR functionality with configuration management
    """
    
    def __init__(self, config: Optional[OCRConfig] = None):
        """
        Initialize OCR service
        
        Args:
            config: OCR configuration (uses default if None)
        """
        self.config = config or get_ocr_config('default')
        self.ocr_engine = None
        self._initialize_engine()
    
    def _initialize_engine(self):
        """Initialize TrOCR engine with current configuration"""
        try:
            if self.config.use_trocr:
                self.ocr_engine = get_ocr_engine(
                    model_name=self.config.model_name,
                    device=self.config.device,
                    cache_dir=self.config.cache_dir
                )
                logger.info(f"OCR service initialized with TrOCR model: {self.config.model_name}")
            else:
                logger.info("OCR service initialized with GOT-OCR2_0 fallback")
        except Exception as e:
            logger.error(f"Failed to initialize OCR engine: {str(e)}")
            self.ocr_engine = None
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """
        Preprocess image for better OCR results
        
        Args:
            image: Input PIL Image
            
        Returns:
            Preprocessed PIL Image
        """
        try:
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Resize image if configured
            if self.config.resize_images:
                # Calculate new size maintaining aspect ratio
                width, height = image.size
                if height != self.config.target_height:
                    ratio = self.config.target_height / height
                    new_width = int(width * ratio)
                    image = image.resize((new_width, self.config.target_height), Image.Resampling.LANCZOS)
            
            # Enhance contrast if configured
            if self.config.enhance_contrast:
                enhancer = ImageEnhance.Contrast(image)
                image = enhancer.enhance(1.2)  # Increase contrast by 20%
            
            return image
            
        except Exception as e:
            logger.warning(f"Image preprocessing failed: {str(e)}")
            return image
    
    def _fallback_ocr(self, image_input: Union[str, Image.Image, np.ndarray]) -> str:
        """
        Fallback OCR using GOT-OCR2_0
        
        Args:
            image_input: Input image
            
        Returns:
            Recognized text string
        """
        try:
            from modelscope import AutoModel, AutoTokenizer
            
            logger.info("Using GOT-OCR2_0 fallback")
            
            tokenizer = AutoTokenizer.from_pretrained('AI-ModelScope/GOT-OCR2_0', trust_remote_code=True)
            model = AutoModel.from_pretrained('AI-ModelScope/GOT-OCR2_0', trust_remote_code=True,
                                            low_cpu_mem_usage=True, device_map='cuda',
                                            use_safetensors=True, pad_token_id=tokenizer.eos_token_id)
            model = model.eval()
            
            # Handle different input types
            if isinstance(image_input, str):
                image_file = image_input
            else:
                # For PIL Image or numpy array, save temporarily
                if hasattr(image_input, 'save'):
                    temp_path = 'temp_ocr_image.png'
                    image_input.save(temp_path)
                    image_file = temp_path
                else:
                    raise ValueError("Unsupported image input type for GOT-OCR2_0")
            
            res = model.chat(tokenizer, image_file, ocr_type='format')
            
            # Clean up temporary file if created
            if not isinstance(image_input, str) and os.path.exists('temp_ocr_image.png'):
                os.remove('temp_ocr_image.png')
            
            return str(res) if res is not None else ""
            
        except Exception as e:
            logger.error(f"GOT-OCR2_0 fallback failed: {str(e)}")
            return ""
    
    def recognize_single(self, 
                        image_input: Union[str, Image.Image, np.ndarray],
                        preprocess: bool = True) -> str:
        """
        Recognize text from a single image
        
        Args:
            image_input: Input image (file path, PIL Image, or numpy array)
            preprocess: Whether to preprocess the image
            
        Returns:
            Recognized text string
        """
        for attempt in range(self.config.retry_attempts):
            try:
                if self.config.use_trocr and self.ocr_engine:
                    # Use TrOCR
                    if preprocess and not isinstance(image_input, str):
                        # Preprocess image if it's not a file path
                        if isinstance(image_input, np.ndarray):
                            image_input = Image.fromarray(image_input)
                        if isinstance(image_input, Image.Image):
                            image_input = self._preprocess_image(image_input)
                    
                    result = self.ocr_engine.recognize_text(
                        image_input,
                        max_length=self.config.max_length,
                        num_beams=self.config.num_beams,
                        early_stopping=self.config.early_stopping
                    )
                    
                    if result.strip():  # Return if we got a non-empty result
                        return result
                
                elif self.config.fallback_to_got_ocr:
                    # Use fallback OCR
                    result = self._fallback_ocr(image_input)
                    if result.strip():
                        return result
                
            except Exception as e:
                logger.warning(f"OCR attempt {attempt + 1} failed: {str(e)}")
                if attempt == self.config.retry_attempts - 1:
                    logger.error(f"All OCR attempts failed for image")
        
        return ""
    
    def recognize_batch(self, 
                       images: List[Union[str, Image.Image, np.ndarray]],
                       preprocess: bool = True,
                       progress_callback: Optional[Callable[[int, int], None]] = None) -> List[str]:
        """
        Recognize text from multiple images
        
        Args:
            images: List of input images
            preprocess: Whether to preprocess images
            progress_callback: Optional callback for progress updates (current, total)
            
        Returns:
            List of recognized text strings
        """
        if not images:
            return []
        
        results = []
        
        if self.config.use_trocr and self.ocr_engine:
            # Use TrOCR batch processing
            try:
                # Preprocess images if needed
                processed_images = images
                if preprocess:
                    processed_images = []
                    for img in images:
                        if isinstance(img, str):
                            processed_images.append(img)
                        else:
                            if isinstance(img, np.ndarray):
                                img = Image.fromarray(img)
                            if isinstance(img, Image.Image):
                                img = self._preprocess_image(img)
                            processed_images.append(img)
                
                # Process in batches
                for i in range(0, len(processed_images), self.config.batch_size):
                    batch = processed_images[i:i + self.config.batch_size]
                    
                    batch_results = self.ocr_engine.recognize_batch(
                        batch,
                        batch_size=len(batch),
                        max_length=self.config.max_length,
                        num_beams=self.config.num_beams,
                        early_stopping=self.config.early_stopping
                    )
                    
                    results.extend(batch_results)
                    
                    # Update progress
                    if progress_callback:
                        progress_callback(min(i + len(batch), len(images)), len(images))
                
            except Exception as e:
                logger.error(f"Batch OCR failed: {str(e)}")
                # Fallback to single image processing
                results = []
                for i, img in enumerate(images):
                    result = self.recognize_single(img, preprocess=preprocess)
                    results.append(result)
                    
                    if progress_callback:
                        progress_callback(i + 1, len(images))
        
        else:
            # Process images one by one
            for i, img in enumerate(images):
                result = self.recognize_single(img, preprocess=preprocess)
                results.append(result)
                
                if progress_callback:
                    progress_callback(i + 1, len(images))
        
        return results
    
    def recognize_pdf_pages(self, 
                           pdf_path: str,
                           page_range: Optional[tuple] = None,
                           progress_callback: Optional[Callable[[int, int], None]] = None) -> List[str]:
        """
        Recognize text from PDF pages using OCR
        
        Args:
            pdf_path: Path to PDF file
            page_range: Optional tuple (start_page, end_page) for partial processing
            progress_callback: Optional callback for progress updates
            
        Returns:
            List of recognized text strings (one per page)
        """
        try:
            import fitz  # PyMuPDF
            
            # Open PDF
            pdf_document = fitz.open(pdf_path)
            total_pages = pdf_document.page_count
            
            # Determine page range
            start_page = 0
            end_page = total_pages
            if page_range:
                start_page = max(0, page_range[0])
                end_page = min(total_pages, page_range[1])
            
            # Extract images from pages
            page_images = []
            for page_num in range(start_page, end_page):
                page = pdf_document[page_num]
                pix = page.get_pixmap()
                img_data = pix.tobytes()
                img = Image.frombytes("RGB", [pix.width, pix.height], img_data)
                page_images.append(img)
            
            pdf_document.close()
            
            # Perform OCR on all pages
            results = self.recognize_batch(page_images, progress_callback=progress_callback)
            
            return results
            
        except Exception as e:
            logger.error(f"PDF OCR failed: {str(e)}")
            return []
    
    def update_config(self, new_config: OCRConfig):
        """
        Update OCR configuration and reinitialize engine if needed
        
        Args:
            new_config: New OCR configuration
        """
        old_model = self.config.model_name if self.config else None
        self.config = new_config
        
        # Reinitialize engine if model changed
        if old_model != new_config.model_name:
            self._initialize_engine()
    
    def get_engine_info(self) -> Dict[str, Any]:
        """Get information about the OCR engine"""
        if self.ocr_engine:
            return self.ocr_engine.get_model_info()
        else:
            return {
                'model_name': 'fallback',
                'model_path': 'GOT-OCR2_0',
                'device': 'auto',
                'cache_dir': None
            }


# Global OCR service instance
_ocr_service = None

def get_ocr_service(config: Optional[OCRConfig] = None) -> OCRService:
    """
    Get or create global OCR service instance
    
    Args:
        config: OCR configuration
        
    Returns:
        OCRService instance
    """
    global _ocr_service
    
    if _ocr_service is None or (config and config != _ocr_service.config):
        _ocr_service = OCRService(config)
    
    return _ocr_service
