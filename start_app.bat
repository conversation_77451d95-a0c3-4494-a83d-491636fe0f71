@echo off
chcp 65001 >nul
echo ========================================
echo        启动 LLM-Kit 应用
echo ========================================
echo.

cd /d "%~dp0"

:: 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未找到！请确保Python已安装并添加到PATH
    echo.
    pause
    exit /b 1
)

:: 检查主程序文件
if not exist "main.py" (
    echo ❌ main.py文件未找到！
    echo.
    pause
    exit /b 1
)

:: 检查环境变量文件
if not exist ".env" (
    echo ⚠️ .env文件未找到，使用默认配置
    echo 建议复制.env.example为.env并配置相关参数
    echo.
)

:: 创建必要的目录
if not exist "uploads" mkdir uploads
if not exist "temp" mkdir temp
if not exist "outputs" mkdir outputs
if not exist "logs" mkdir logs
if not exist "cache" mkdir cache
if not exist "models_cache" mkdir models_cache

echo 📋 应用配置信息:
echo    - 主机: 127.0.0.1
echo    - 端口: 8000
echo    - 环境: %cd%
echo.

echo 🔍 检查MongoDB连接...
python -c "import pymongo; client = pymongo.MongoClient('mongodb://localhost:27017'); client.admin.command('ping'); print('✅ MongoDB连接正常')" 2>nul
if errorlevel 1 (
    echo ❌ MongoDB连接失败！
    echo 请确保MongoDB正在运行 (运行 start_mongodb.bat)
    echo.
    pause
    exit /b 1
)

echo 🚀 正在启动LLM-Kit应用...
echo.
echo 📱 应用地址: http://127.0.0.1:8000
echo 📚 API文档: http://127.0.0.1:8000/docs
echo 🎯 前端页面: http://127.0.0.1:8000 (如果有前端)
echo.
echo 按 Ctrl+C 停止应用
echo ========================================
echo.

:: 启动应用
python main.py

:: 如果应用异常退出
echo.
echo ⚠️ 应用已停止运行
echo.
pause
