"""
TrOCR-based OCR implementation for text recognition from images
Using Hugging Face Transformers TrOCR models
"""

import os
import logging
from typing import Union, List, Optional, Tuple
from PIL import Image
import torch
from transformers import TrOCRProcessor, VisionEncoderDecoderModel
import numpy as np

# Configure logging
logger = logging.getLogger(__name__)

class TrOCREngine:
    """
    TrOCR-based OCR engine for text recognition from images
    Supports multiple TrOCR model variants for different use cases
    """
    
    # Available TrOCR models
    MODELS = {
        'base': 'microsoft/trocr-base-printed',           # Base model for printed text
        'large': 'microsoft/trocr-large-printed',        # Large model for printed text  
        'handwritten': 'microsoft/trocr-base-handwritten', # Handwritten text
        'stage1': 'microsoft/trocr-base-stage1',         # Stage 1 pre-trained model
        'str': 'microsoft/trocr-small-str',              # Small model for scene text
    }
    
    def __init__(self, 
                 model_name: str = 'base',
                 device: Optional[str] = None,
                 cache_dir: Optional[str] = None):
        """
        Initialize TrOCR engine
        
        Args:
            model_name: Model variant to use ('base', 'large', 'handwritten', 'stage1', 'str')
            device: Device to run model on ('cuda', 'cpu', or None for auto-detection)
            cache_dir: Directory to cache downloaded models
        """
        self.model_name = model_name
        self.cache_dir = cache_dir
        
        # Auto-detect device if not specified
        if device is None:
            self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        else:
            self.device = device
            
        logger.info(f"Initializing TrOCR engine with model: {model_name}, device: {self.device}")
        
        # Validate model name
        if model_name not in self.MODELS:
            raise ValueError(f"Unsupported model: {model_name}. Available models: {list(self.MODELS.keys())}")
        
        self.model_path = self.MODELS[model_name]
        self.processor = None
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """Load TrOCR processor and model"""
        try:
            logger.info(f"Loading TrOCR model: {self.model_path}")
            
            # Load processor and model
            self.processor = TrOCRProcessor.from_pretrained(
                self.model_path,
                cache_dir=self.cache_dir
            )
            
            self.model = VisionEncoderDecoderModel.from_pretrained(
                self.model_path,
                cache_dir=self.cache_dir
            )
            
            # Move model to specified device
            self.model.to(self.device)
            self.model.eval()
            
            logger.info(f"TrOCR model loaded successfully on {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to load TrOCR model: {str(e)}")
            raise
    
    def preprocess_image(self, image: Union[str, Image.Image, np.ndarray]) -> Image.Image:
        """
        Preprocess image for OCR
        
        Args:
            image: Input image (file path, PIL Image, or numpy array)
            
        Returns:
            Preprocessed PIL Image
        """
        if isinstance(image, str):
            # Load from file path
            if not os.path.exists(image):
                raise FileNotFoundError(f"Image file not found: {image}")
            image = Image.open(image)
        elif isinstance(image, np.ndarray):
            # Convert numpy array to PIL Image
            image = Image.fromarray(image)
        elif not isinstance(image, Image.Image):
            raise TypeError(f"Unsupported image type: {type(image)}")
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        return image
    
    def recognize_text(self, 
                      image: Union[str, Image.Image, np.ndarray],
                      max_length: int = 512,
                      num_beams: int = 4,
                      early_stopping: bool = True) -> str:
        """
        Recognize text from a single image
        
        Args:
            image: Input image
            max_length: Maximum length of generated text
            num_beams: Number of beams for beam search
            early_stopping: Whether to stop early in beam search
            
        Returns:
            Recognized text string
        """
        try:
            # Preprocess image
            processed_image = self.preprocess_image(image)
            
            # Process image with TrOCR processor
            pixel_values = self.processor(
                images=processed_image, 
                return_tensors="pt"
            ).pixel_values.to(self.device)
            
            # Generate text
            with torch.no_grad():
                generated_ids = self.model.generate(
                    pixel_values,
                    max_length=max_length,
                    num_beams=num_beams,
                    early_stopping=early_stopping
                )
            
            # Decode generated text
            generated_text = self.processor.batch_decode(
                generated_ids, 
                skip_special_tokens=True
            )[0]
            
            return generated_text.strip()
            
        except Exception as e:
            logger.error(f"Error during text recognition: {str(e)}")
            return ""
    
    def recognize_batch(self, 
                       images: List[Union[str, Image.Image, np.ndarray]],
                       batch_size: int = 4,
                       max_length: int = 512,
                       num_beams: int = 4,
                       early_stopping: bool = True) -> List[str]:
        """
        Recognize text from multiple images in batches
        
        Args:
            images: List of input images
            batch_size: Batch size for processing
            max_length: Maximum length of generated text
            num_beams: Number of beams for beam search
            early_stopping: Whether to stop early in beam search
            
        Returns:
            List of recognized text strings
        """
        results = []
        
        for i in range(0, len(images), batch_size):
            batch_images = images[i:i + batch_size]
            batch_results = []
            
            try:
                # Preprocess batch images
                processed_images = [self.preprocess_image(img) for img in batch_images]
                
                # Process batch with TrOCR processor
                pixel_values = self.processor(
                    images=processed_images, 
                    return_tensors="pt"
                ).pixel_values.to(self.device)
                
                # Generate text for batch
                with torch.no_grad():
                    generated_ids = self.model.generate(
                        pixel_values,
                        max_length=max_length,
                        num_beams=num_beams,
                        early_stopping=early_stopping
                    )
                
                # Decode generated texts
                generated_texts = self.processor.batch_decode(
                    generated_ids, 
                    skip_special_tokens=True
                )
                
                batch_results = [text.strip() for text in generated_texts]
                
            except Exception as e:
                logger.error(f"Error processing batch {i//batch_size + 1}: {str(e)}")
                # Add empty strings for failed batch
                batch_results = [""] * len(batch_images)
            
            results.extend(batch_results)
        
        return results
    
    def get_model_info(self) -> dict:
        """Get information about the loaded model"""
        return {
            'model_name': self.model_name,
            'model_path': self.model_path,
            'device': self.device,
            'cache_dir': self.cache_dir
        }


# Global OCR engine instance (lazy loading)
_ocr_engine = None

def get_ocr_engine(model_name: str = 'base', 
                   device: Optional[str] = None,
                   cache_dir: Optional[str] = None) -> TrOCREngine:
    """
    Get or create global OCR engine instance
    
    Args:
        model_name: Model variant to use
        device: Device to run model on
        cache_dir: Directory to cache downloaded models
        
    Returns:
        TrOCREngine instance
    """
    global _ocr_engine
    
    if _ocr_engine is None or _ocr_engine.model_name != model_name:
        _ocr_engine = TrOCREngine(
            model_name=model_name,
            device=device,
            cache_dir=cache_dir
        )
    
    return _ocr_engine


def single_ocr(image: Union[str, Image.Image, np.ndarray],
               model_name: str = 'base',
               device: Optional[str] = None) -> str:
    """
    Convenience function for single image OCR
    Compatible with existing single_ocr interface
    
    Args:
        image: Input image (file path, PIL Image, or numpy array)
        model_name: TrOCR model variant to use
        device: Device to run model on
        
    Returns:
        Recognized text string
    """
    try:
        ocr_engine = get_ocr_engine(model_name=model_name, device=device)
        return ocr_engine.recognize_text(image)
    except Exception as e:
        logger.error(f"OCR failed: {str(e)}")
        return ""


def batch_ocr(images: List[Union[str, Image.Image, np.ndarray]],
              model_name: str = 'base',
              device: Optional[str] = None,
              batch_size: int = 4) -> List[str]:
    """
    Convenience function for batch OCR
    
    Args:
        images: List of input images
        model_name: TrOCR model variant to use
        device: Device to run model on
        batch_size: Batch size for processing
        
    Returns:
        List of recognized text strings
    """
    try:
        ocr_engine = get_ocr_engine(model_name=model_name, device=device)
        return ocr_engine.recognize_batch(images, batch_size=batch_size)
    except Exception as e:
        logger.error(f"Batch OCR failed: {str(e)}")
        return [""] * len(images)
