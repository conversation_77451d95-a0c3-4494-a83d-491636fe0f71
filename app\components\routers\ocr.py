"""
OCR API routes for TrOCR integration
Provides REST API endpoints for OCR functionality
"""

import os
import logging
import tempfile
from typing import List, Optional
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends, Query
from fastapi.responses import JSONResponse
from motor.motor_asyncio import AsyncIOMotorClient
from PIL import Image
import base64
import io

from app.components.core.database import get_database
from app.components.models.schemas import APIResponse
from text_parse.ocr_service import get_ocr_service
from text_parse.ocr_config import get_ocr_config, list_available_configs
from text_parse.parse import single_ocr, batch_ocr

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/ocr/models")
async def list_ocr_models():
    """List available OCR models and configurations"""
    try:
        models = {
            'trocr_models': [
                {
                    'name': 'base',
                    'description': 'Base TrOCR model for printed text',
                    'model_path': 'microsoft/trocr-base-printed'
                },
                {
                    'name': 'large', 
                    'description': 'Large TrOCR model for printed text',
                    'model_path': 'microsoft/trocr-large-printed'
                },
                {
                    'name': 'handwritten',
                    'description': 'TrOCR model for handwritten text',
                    'model_path': 'microsoft/trocr-base-handwritten'
                },
                {
                    'name': 'stage1',
                    'description': 'Stage 1 pre-trained TrOCR model',
                    'model_path': 'microsoft/trocr-base-stage1'
                },
                {
                    'name': 'str',
                    'description': 'Small TrOCR model for scene text',
                    'model_path': 'microsoft/trocr-small-str'
                }
            ],
            'configurations': list_available_configs(),
            'fallback_model': 'GOT-OCR2_0'
        }
        
        return APIResponse(
            status="success",
            message="OCR models listed successfully",
            data=models
        )
        
    except Exception as e:
        logger.error(f"Failed to list OCR models: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ocr/single")
async def ocr_single_image(
    file: UploadFile = File(...),
    model_name: str = Form(default="base"),
    use_trocr: bool = Form(default=True),
    config_name: str = Form(default="default")
):
    """
    Perform OCR on a single uploaded image
    
    Args:
        file: Uploaded image file
        model_name: TrOCR model variant to use
        use_trocr: Whether to use TrOCR (fallback to GOT-OCR2_0 if False)
        config_name: OCR configuration preset to use
    """
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Read image file
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # Perform OCR
        result = single_ocr(
            image_input=image,
            model_name=model_name,
            use_trocr=use_trocr
        )
        
        return APIResponse(
            status="success",
            message="OCR completed successfully",
            data={
                "filename": file.filename,
                "model_name": model_name,
                "use_trocr": use_trocr,
                "config_name": config_name,
                "recognized_text": result,
                "text_length": len(result)
            }
        )
        
    except Exception as e:
        logger.error(f"Single image OCR failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ocr/batch")
async def ocr_batch_images(
    files: List[UploadFile] = File(...),
    model_name: str = Form(default="base"),
    use_trocr: bool = Form(default=True),
    config_name: str = Form(default="default")
):
    """
    Perform OCR on multiple uploaded images
    
    Args:
        files: List of uploaded image files
        model_name: TrOCR model variant to use
        use_trocr: Whether to use TrOCR
        config_name: OCR configuration preset to use
    """
    try:
        if len(files) > 50:  # Limit batch size
            raise HTTPException(status_code=400, detail="Maximum 50 files allowed per batch")
        
        # Process all images
        images = []
        filenames = []
        
        for file in files:
            if not file.content_type.startswith('image/'):
                logger.warning(f"Skipping non-image file: {file.filename}")
                continue
                
            image_data = await file.read()
            image = Image.open(io.BytesIO(image_data))
            images.append(image)
            filenames.append(file.filename)
        
        if not images:
            raise HTTPException(status_code=400, detail="No valid image files found")
        
        # Perform batch OCR
        results = batch_ocr(
            images=images,
            model_name=model_name,
            use_trocr=use_trocr
        )
        
        # Combine results with filenames
        ocr_results = []
        for filename, text in zip(filenames, results):
            ocr_results.append({
                "filename": filename,
                "recognized_text": text,
                "text_length": len(text)
            })
        
        return APIResponse(
            status="success",
            message=f"Batch OCR completed for {len(ocr_results)} images",
            data={
                "model_name": model_name,
                "use_trocr": use_trocr,
                "config_name": config_name,
                "total_images": len(ocr_results),
                "results": ocr_results
            }
        )
        
    except Exception as e:
        logger.error(f"Batch OCR failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ocr/base64")
async def ocr_base64_image(
    image_data: str = Form(...),
    model_name: str = Form(default="base"),
    use_trocr: bool = Form(default=True),
    config_name: str = Form(default="default")
):
    """
    Perform OCR on a base64-encoded image
    
    Args:
        image_data: Base64-encoded image data
        model_name: TrOCR model variant to use
        use_trocr: Whether to use TrOCR
        config_name: OCR configuration preset to use
    """
    try:
        # Decode base64 image
        try:
            # Remove data URL prefix if present
            if image_data.startswith('data:image'):
                image_data = image_data.split(',')[1]
            
            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Invalid base64 image data: {str(e)}")
        
        # Perform OCR
        result = single_ocr(
            image_input=image,
            model_name=model_name,
            use_trocr=use_trocr
        )
        
        return APIResponse(
            status="success",
            message="OCR completed successfully",
            data={
                "model_name": model_name,
                "use_trocr": use_trocr,
                "config_name": config_name,
                "recognized_text": result,
                "text_length": len(result)
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Base64 OCR failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ocr/pdf")
async def ocr_pdf_file(
    file: UploadFile = File(...),
    model_name: str = Form(default="base"),
    use_trocr: bool = Form(default=True),
    start_page: int = Form(default=0),
    end_page: Optional[int] = Form(default=None),
    db: AsyncIOMotorClient = Depends(get_database)
):
    """
    Perform OCR on PDF file pages
    
    Args:
        file: Uploaded PDF file
        model_name: TrOCR model variant to use
        use_trocr: Whether to use TrOCR
        start_page: Starting page number (0-based)
        end_page: Ending page number (None for all pages)
        db: Database connection
    """
    try:
        # Validate file type
        if not file.content_type == 'application/pdf':
            raise HTTPException(status_code=400, detail="File must be a PDF")
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Get OCR service
            config = get_ocr_config('default')
            config.model_name = model_name
            config.use_trocr = use_trocr
            
            ocr_service = get_ocr_service(config)
            
            # Determine page range
            page_range = None
            if end_page is not None:
                page_range = (start_page, end_page)
            elif start_page > 0:
                page_range = (start_page, None)
            
            # Perform OCR on PDF
            results = ocr_service.recognize_pdf_pages(
                pdf_path=temp_file_path,
                page_range=page_range
            )
            
            # Prepare response data
            page_results = []
            for i, text in enumerate(results):
                page_results.append({
                    "page_number": start_page + i,
                    "recognized_text": text,
                    "text_length": len(text)
                })
            
            return APIResponse(
                status="success",
                message=f"PDF OCR completed for {len(results)} pages",
                data={
                    "filename": file.filename,
                    "model_name": model_name,
                    "use_trocr": use_trocr,
                    "total_pages": len(results),
                    "start_page": start_page,
                    "end_page": start_page + len(results) - 1 if results else start_page,
                    "pages": page_results
                }
            )
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"PDF OCR failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ocr/config/{config_name}")
async def get_ocr_configuration(config_name: str):
    """Get OCR configuration details"""
    try:
        config = get_ocr_config(config_name)
        
        return APIResponse(
            status="success",
            message=f"OCR configuration '{config_name}' retrieved successfully",
            data=config.to_dict()
        )
        
    except Exception as e:
        logger.error(f"Failed to get OCR configuration: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ocr/status")
async def get_ocr_status():
    """Get OCR service status and information"""
    try:
        # Get OCR service info
        ocr_service = get_ocr_service()
        engine_info = ocr_service.get_engine_info()
        
        return APIResponse(
            status="success",
            message="OCR service status retrieved successfully",
            data={
                "service_status": "active",
                "engine_info": engine_info,
                "available_configs": list_available_configs(),
                "supported_formats": ["jpg", "jpeg", "png", "bmp", "tiff", "pdf"]
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get OCR status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
