#!/bin/bash

echo "========================================"
echo "          启动 MongoDB 数据库"
echo "========================================"
echo

cd "$(dirname "$0")"

# 检查MongoDB目录是否存在
if [ ! -f "mongodb/bin/mongod" ]; then
    echo "❌ MongoDB未找到！"
    echo "请先运行 python setup_environment.py 安装MongoDB"
    echo
    exit 1
fi

# 创建必要的目录
mkdir -p mongodb_data
mkdir -p mongodb_logs

# 检查配置文件
if [ ! -f "mongod.conf" ]; then
    echo "❌ MongoDB配置文件未找到！"
    echo "请确保 mongod.conf 文件存在"
    echo
    exit 1
fi

echo "📋 MongoDB配置信息:"
echo "   - 数据目录: $(pwd)/mongodb_data"
echo "   - 日志目录: $(pwd)/mongodb_logs"
echo "   - 端口: 27017"
echo "   - 绑定IP: 127.0.0.1"
echo

echo "🚀 正在启动MongoDB..."
echo

# 启动MongoDB
./mongodb/bin/mongod --config mongod.conf

# 如果MongoDB异常退出
echo
echo "⚠️ MongoDB已停止运行"
echo
