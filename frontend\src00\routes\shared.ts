export const eval_index_full_name = {
  A: "Accuracy",
  B: "BLEU",
  D: "Distinct-2",
  F: "F1-Score",
  R: "ROUGHE",
  P: "Precision",
};

export const default_model_list = [{
  model_display_name: "llama-3-8B",
  model_name: "Meta-Llama-3-8B",
  source: "git",
  model_description: "Meta developed and released the Meta Llama 3 family of large language models (LLMs)",
  download_url: "https://modelscope.cn/LLM-Research/Meta-Llama-3-8B.git",
  avatar_url: "https://img1.baidu.com/it/u=3820215648,1880915447&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1715619600&t=bf06d03d9ff164bf4dfa30a1f5a7fd3f"
},
{
  model_display_name: "gemma-2B",
  model_name: "gemma-2b",
  source: "git",
  model_description: "2B base version of the Gemma modelmodels (LLMs)",
  download_url: "https://modelscope.cn/AI-ModelScope/gemma-2b",
  avatar_url: "111"
},
// {
//   model_display_name: "llama-2-7B",
//   model_name: "Llama-2-7b-hf",
//   source: "git",
//   model_description: "Meta developed and released the Meta Llama 2 family of large language models (LLMs)",
//   download_url: "https://modelscope.cn/LLM-Research/Meta-Llama-3-8B.git",
//   avatar_url: "https://img1.baidu.com/it/u=3820215648,1880915447&fm=253&app=120&size=w931&n=0&f=JPEG&fmt=auto?sec=1715619600&t=bf06d03d9ff164bf4dfa30a1f5a7fd3f"
// },
{
  model_display_name: "Mistral-7B",
  model_name: "Mistral-7B",
  source: "git",
  model_description: "Mistral-7B-v0.1 Large Language Model (LLM)",
  download_url: "https://modelscope.cn/TabbyML/Mistral-7B.git",
  avatar_url: ""
},
{
  model_display_name: "bloomz-7b1",
  model_name: "bloomz-7b1",
  source: "git",
  model_description: "bloomz-7b1",
  download_url: "https://modelscope.cn/mAI-ModelScope/bloomz-7b1.git",
  avatar_url: "https://modelscope.cn/mAI-ModelScope/bloomz-7b1"
},
{
  model_display_name: "gemma-7b",
  model_name: "gemma-7b",
  source: "git",
  model_description: "This model card corresponds to the 7B base version of the Gemma model.",
  download_url: "https://modelscope.cn/AI-ModelScope/gemma-7b.git",
  avatar_url: ""
},
{
  model_display_name: "Yi-6B",
  model_name: "Yi-6B",
  source: "git",
  model_description: "Yi-6B",
  download_url: "https://modelscope.cn/01ai/Yi-6B.git",
  avatar_url: ""
}
]