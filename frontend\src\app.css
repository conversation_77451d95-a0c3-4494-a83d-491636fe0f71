/* Write your global styles here, in PostCSS syntax */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 中文字体优化 */
@layer base {
  html {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  }

  body {
    font-feature-settings: "kern" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 中文文本优化 */
  .chinese-text {
    line-height: 1.7;
    letter-spacing: 0.05em;
  }

  /* 标题字体优化 */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.4;
  }

  /* 按钮文字优化 */
  button, .btn {
    font-weight: 500;
    letter-spacing: 0.02em;
  }
}
