# PowerShell startup script for MongoDB
# Run with: powershell -ExecutionPolicy Bypass -File start_mongodb.ps1

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    MongoDB PowerShell Startup Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Change to project directory
$ProjectDir = "d:\pythonprojs\LLM-Kit"
Set-Location $ProjectDir
Write-Host "[INFO] Working directory: $(Get-Location)" -ForegroundColor Green
Write-Host ""

# Check if MongoDB exists
$MongoPath = "mongodb\bin\mongod.exe"
if (-not (Test-Path $MongoPath)) {
    Write-Host "[ERROR] MongoDB not found at: $(Get-Location)\$MongoPath" -ForegroundColor Red
    Write-Host ""
    Write-Host "[SOLUTION] Download and install MongoDB:" -ForegroundColor Yellow
    Write-Host "1. Download MongoDB Community Server from: https://www.mongodb.com/try/download/community" -ForegroundColor White
    Write-Host "2. Extract to project directory as 'mongodb' folder" -ForegroundColor White
    Write-Host "3. Ensure mongod.exe is at: mongodb\bin\mongod.exe" -ForegroundColor White
    Write-Host ""
    Write-Host "[ALTERNATIVE] Use system MongoDB if installed:" -ForegroundColor Yellow
    Write-Host "- Check if 'mongod' command works in command prompt" -ForegroundColor White
    Write-Host "- If yes, you can start MongoDB with: mongod --dbpath mongodb_data" -ForegroundColor White
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Create directories
Write-Host "[SETUP] Creating MongoDB directories..." -ForegroundColor Yellow
$directories = @("mongodb_data", "mongodb_logs")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "[CREATED] $dir" -ForegroundColor Green
    } else {
        Write-Host "[EXISTS] $dir" -ForegroundColor Gray
    }
}
Write-Host ""

# Check if MongoDB is already running
Write-Host "[CHECK] Checking if MongoDB is already running..." -ForegroundColor Yellow
$mongoProcess = Get-NetTCPConnection -LocalPort 27017 -ErrorAction SilentlyContinue
if ($mongoProcess) {
    Write-Host "[WARNING] Port 27017 is already in use" -ForegroundColor Yellow
    Write-Host "[INFO] MongoDB may already be running" -ForegroundColor Cyan
    Write-Host "[INFO] You can continue or stop the existing process" -ForegroundColor Cyan
    Write-Host ""
}

# Create simple config if not exists
if (-not (Test-Path "mongod.conf")) {
    Write-Host "[INFO] Creating simple MongoDB configuration..." -ForegroundColor Yellow
    $configContent = @"
# Simple MongoDB configuration
storage:
  dbPath: ./mongodb_data
systemLog:
  destination: file
  path: ./mongodb_logs/mongod.log
net:
  port: 27017
  bindIp: 127.0.0.1
"@
    $configContent | Out-File -FilePath "mongod.conf" -Encoding UTF8
    Write-Host "[SUCCESS] Configuration created" -ForegroundColor Green
}
Write-Host ""

# Start MongoDB
Write-Host "[STARTING] Starting MongoDB..." -ForegroundColor Green
Write-Host "[INFO] Data directory: $(Get-Location)\mongodb_data" -ForegroundColor Cyan
Write-Host "[INFO] Log file: $(Get-Location)\mongodb_logs\mongod.log" -ForegroundColor Cyan
Write-Host "[INFO] Port: 27017" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press Ctrl+C to stop MongoDB" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

try {
    & ".\$MongoPath" --config mongod.conf
} catch {
    Write-Host ""
    Write-Host "[ERROR] MongoDB failed to start" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "[TROUBLESHOOTING]" -ForegroundColor Yellow
    Write-Host "- Check if port 27017 is available" -ForegroundColor White
    Write-Host "- Check mongodb_logs\mongod.log for detailed error messages" -ForegroundColor White
    Write-Host "- Ensure sufficient disk space" -ForegroundColor White
    Write-Host "- Try running as administrator" -ForegroundColor White
}

Write-Host ""
Write-Host "[STOPPED] MongoDB stopped" -ForegroundColor Yellow
Read-Host "Press Enter to exit"
