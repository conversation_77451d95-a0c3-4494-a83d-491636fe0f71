#!/bin/bash

echo "========================================"
echo "        启动 LLM-Kit 应用"
echo "========================================"
echo

cd "$(dirname "$0")"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python未找到！请确保Python已安装"
        echo
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# 检查主程序文件
if [ ! -f "main.py" ]; then
    echo "❌ main.py文件未找到！"
    echo
    exit 1
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "⚠️ .env文件未找到，使用默认配置"
    echo "建议复制.env.example为.env并配置相关参数"
    echo
fi

# 创建必要的目录
mkdir -p uploads
mkdir -p temp
mkdir -p outputs
mkdir -p logs
mkdir -p cache
mkdir -p models_cache

echo "📋 应用配置信息:"
echo "   - 主机: 127.0.0.1"
echo "   - 端口: 8000"
echo "   - 环境: $(pwd)"
echo "   - Python: $PYTHON_CMD"
echo

echo "🔍 检查MongoDB连接..."
$PYTHON_CMD -c "import pymongo; client = pymongo.MongoClient('mongodb://localhost:27017'); client.admin.command('ping'); print('✅ MongoDB连接正常')" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ MongoDB连接失败！"
    echo "请确保MongoDB正在运行 (运行 ./start_mongodb.sh)"
    echo
    exit 1
fi

echo "🚀 正在启动LLM-Kit应用..."
echo
echo "📱 应用地址: http://127.0.0.1:8000"
echo "📚 API文档: http://127.0.0.1:8000/docs"
echo "🎯 前端页面: http://127.0.0.1:8000 (如果有前端)"
echo
echo "按 Ctrl+C 停止应用"
echo "========================================"
echo

# 启动应用
$PYTHON_CMD main.py

# 如果应用异常退出
echo
echo "⚠️ 应用已停止运行"
echo
