@echo off
:: Simple MongoDB startup script
chcp 65001 >nul 2>&1

echo.
echo ========================================
echo      MongoDB Simple Startup Script
echo ========================================
echo.

:: Change to project directory
cd /d "d:\pythonprojs\LLM-Kit"
echo [INFO] Working directory: %cd%
echo.

:: Check if MongoDB exists
if not exist "mongodb\bin\mongod.exe" (
    echo [ERROR] MongoDB not found at: %cd%\mongodb\bin\mongod.exe
    echo.
    echo [SOLUTION] Download and install MongoDB:
    echo 1. Download MongoDB Community Server from: https://www.mongodb.com/try/download/community
    echo 2. Extract to project directory as 'mongodb' folder
    echo 3. Ensure mongod.exe is at: mongodb\bin\mongod.exe
    echo.
    echo [ALTERNATIVE] Use system MongoDB if installed:
    echo - Check if 'mongod' command works in command prompt
    echo - If yes, you can start MongoDB with: mongod --dbpath mongodb_data
    echo.
    pause
    exit /b 1
)

:: Create directories
echo [SETUP] Creating MongoDB directories...
if not exist "mongodb_data" mkdir mongodb_data
if not exist "mongodb_logs" mkdir mongodb_logs
echo [SUCCESS] Directories created
echo.

:: Check if MongoDB is already running
echo [CHECK] Checking if MongoDB is already running...
netstat -an | findstr ":27017" >nul 2>&1
if not errorlevel 1 (
    echo [WARNING] Port 27017 is already in use
    echo [INFO] MongoDB may already be running
    echo [INFO] You can continue or stop the existing process
    echo.
)

:: Create simple config if not exists
if not exist "mongod.conf" (
    echo [INFO] Creating simple MongoDB configuration...
    echo # Simple MongoDB configuration > mongod.conf
    echo storage: >> mongod.conf
    echo   dbPath: ./mongodb_data >> mongod.conf
    echo systemLog: >> mongod.conf
    echo   destination: file >> mongod.conf
    echo   path: ./mongodb_logs/mongod.log >> mongod.conf
    echo net: >> mongod.conf
    echo   port: 27017 >> mongod.conf
    echo   bindIp: 127.0.0.1 >> mongod.conf
    echo [SUCCESS] Configuration created
)

:: Start MongoDB
echo [STARTING] Starting MongoDB...
echo [INFO] Data directory: %cd%\mongodb_data
echo [INFO] Log file: %cd%\mongodb_logs\mongod.log
echo [INFO] Port: 27017
echo.
echo Press Ctrl+C to stop MongoDB
echo ========================================
echo.

mongodb\bin\mongod.exe --config mongod.conf

echo.
echo [STOPPED] MongoDB stopped
pause
