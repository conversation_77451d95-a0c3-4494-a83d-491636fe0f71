#!/usr/bin/env python3
"""
数据库初始化脚本
创建LLM-Kit项目所需的MongoDB数据库和集合
"""

import asyncio
import logging
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import CollectionInvalid
import os
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseInitializer:
    def __init__(self, mongodb_url="mongodb://localhost:27017", database_name="llm_kit"):
        self.mongodb_url = mongodb_url
        self.database_name = database_name
        self.client = None
        self.db = None
    
    async def connect(self):
        """连接到MongoDB"""
        try:
            self.client = AsyncIOMotorClient(self.mongodb_url)
            self.db = self.client[self.database_name]
            
            # 测试连接
            await self.client.admin.command('ping')
            logger.info(f"✅ 成功连接到MongoDB: {self.mongodb_url}")
            logger.info(f"✅ 使用数据库: {self.database_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 连接MongoDB失败: {e}")
            return False
    
    async def create_collections(self):
        """创建所需的集合"""
        logger.info("📋 创建数据库集合...")
        
        collections = [
            # 文件解析相关
            {
                "name": "parse_records",
                "description": "文件解析记录",
                "indexes": [
                    {"keys": [("file_path", 1)], "unique": True},
                    {"keys": [("created_at", -1)]},
                    {"keys": [("status", 1)]},
                    {"keys": [("file_type", 1)]}
                ]
            },
            
            # LaTeX转换相关
            {
                "name": "tex_records", 
                "description": "LaTeX转换记录",
                "indexes": [
                    {"keys": [("source_file", 1)]},
                    {"keys": [("created_at", -1)]},
                    {"keys": [("status", 1)]}
                ]
            },
            
            # QA生成相关
            {
                "name": "qa_generations",
                "description": "QA生成任务记录",
                "indexes": [
                    {"keys": [("task_id", 1)], "unique": True},
                    {"keys": [("created_at", -1)]},
                    {"keys": [("status", 1)]},
                    {"keys": [("model_name", 1)]}
                ]
            },
            
            {
                "name": "qa_pairs",
                "description": "生成的QA对数据",
                "indexes": [
                    {"keys": [("generation_id", 1)]},
                    {"keys": [("question", "text")]},
                    {"keys": [("created_at", -1)]},
                    {"keys": [("quality_score", -1)]}
                ]
            },
            
            # 质量控制相关
            {
                "name": "quality_generations",
                "description": "质量控制记录",
                "indexes": [
                    {"keys": [("source_generation_id", 1)]},
                    {"keys": [("created_at", -1)]},
                    {"keys": [("status", 1)]},
                    {"keys": [("quality_threshold", 1)]}
                ]
            },
            
            # 去重相关
            {
                "name": "dedup_records",
                "description": "去重处理记录",
                "indexes": [
                    {"keys": [("source_file", 1)]},
                    {"keys": [("created_at", -1)]},
                    {"keys": [("dedup_method", 1)]},
                    {"keys": [("threshold", 1)]}
                ]
            },
            
            # 文件管理相关
            {
                "name": "uploaded_files",
                "description": "上传文件记录",
                "indexes": [
                    {"keys": [("filename", 1)]},
                    {"keys": [("file_hash", 1)], "unique": True},
                    {"keys": [("upload_time", -1)]},
                    {"keys": [("file_type", 1)]},
                    {"keys": [("file_size", 1)]}
                ]
            },
            
            # 数据集管理相关
            {
                "name": "datasets",
                "description": "数据集记录",
                "indexes": [
                    {"keys": [("name", 1)], "unique": True},
                    {"keys": [("created_at", -1)]},
                    {"keys": [("dataset_type", 1)]},
                    {"keys": [("status", 1)]}
                ]
            },
            
            # COT生成相关
            {
                "name": "cot_generations",
                "description": "思维链生成记录",
                "indexes": [
                    {"keys": [("task_id", 1)], "unique": True},
                    {"keys": [("created_at", -1)]},
                    {"keys": [("model_name", 1)]},
                    {"keys": [("domain", 1)]}
                ]
            },
            
            # 错误日志
            {
                "name": "error_logs",
                "description": "系统错误日志",
                "indexes": [
                    {"keys": [("timestamp", -1)]},
                    {"keys": [("level", 1)]},
                    {"keys": [("module", 1)]},
                    {"keys": [("error_type", 1)]}
                ]
            },
            
            # 系统配置
            {
                "name": "system_config",
                "description": "系统配置信息",
                "indexes": [
                    {"keys": [("config_key", 1)], "unique": True},
                    {"keys": [("updated_at", -1)]}
                ]
            },
            
            # 用户会话 (如果需要)
            {
                "name": "user_sessions",
                "description": "用户会话信息",
                "indexes": [
                    {"keys": [("session_id", 1)], "unique": True},
                    {"keys": [("created_at", -1)]},
                    {"keys": [("last_activity", -1)]}
                ]
            }
        ]
        
        for collection_info in collections:
            collection_name = collection_info["name"]
            description = collection_info["description"]
            
            try:
                # 创建集合
                collection = self.db[collection_name]
                
                # 检查集合是否已存在
                existing_collections = await self.db.list_collection_names()
                if collection_name not in existing_collections:
                    await self.db.create_collection(collection_name)
                    logger.info(f"✅ 创建集合: {collection_name} ({description})")
                else:
                    logger.info(f"⚠️ 集合已存在: {collection_name}")
                
                # 创建索引
                if "indexes" in collection_info:
                    for index_info in collection_info["indexes"]:
                        try:
                            await collection.create_index(
                                index_info["keys"],
                                unique=index_info.get("unique", False),
                                background=True
                            )
                            logger.info(f"  ✅ 创建索引: {index_info['keys']}")
                        except Exception as e:
                            logger.warning(f"  ⚠️ 索引创建失败: {e}")
                
            except CollectionInvalid:
                logger.info(f"⚠️ 集合已存在: {collection_name}")
            except Exception as e:
                logger.error(f"❌ 创建集合失败 {collection_name}: {e}")
    
    async def insert_initial_data(self):
        """插入初始数据"""
        logger.info("📋 插入初始数据...")
        
        # 系统配置初始数据
        initial_configs = [
            {
                "config_key": "app_version",
                "config_value": "1.0.0",
                "description": "应用版本号",
                "updated_at": datetime.utcnow()
            },
            {
                "config_key": "default_model",
                "config_value": "base",
                "description": "默认OCR模型",
                "updated_at": datetime.utcnow()
            },
            {
                "config_key": "max_file_size",
                "config_value": 100 * 1024 * 1024,  # 100MB
                "description": "最大文件上传大小(字节)",
                "updated_at": datetime.utcnow()
            },
            {
                "config_key": "supported_file_types",
                "config_value": ["pdf", "txt", "png", "jpg", "jpeg", "bmp", "tiff"],
                "description": "支持的文件类型",
                "updated_at": datetime.utcnow()
            }
        ]
        
        config_collection = self.db["system_config"]
        for config in initial_configs:
            try:
                await config_collection.update_one(
                    {"config_key": config["config_key"]},
                    {"$setOnInsert": config},
                    upsert=True
                )
                logger.info(f"✅ 配置项: {config['config_key']}")
            except Exception as e:
                logger.error(f"❌ 插入配置失败: {e}")
    
    async def create_database_info(self):
        """创建数据库信息文档"""
        logger.info("📋 创建数据库信息...")
        
        db_info = {
            "database_name": self.database_name,
            "created_at": datetime.utcnow(),
            "version": "1.0.0",
            "description": "LLM-Kit项目数据库",
            "collections_count": len(await self.db.list_collection_names()),
            "initialized": True
        }
        
        try:
            await self.db["_database_info"].replace_one(
                {"database_name": self.database_name},
                db_info,
                upsert=True
            )
            logger.info("✅ 数据库信息创建完成")
        except Exception as e:
            logger.error(f"❌ 创建数据库信息失败: {e}")
    
    async def verify_setup(self):
        """验证数据库设置"""
        logger.info("🔍 验证数据库设置...")
        
        try:
            # 检查集合数量
            collections = await self.db.list_collection_names()
            logger.info(f"✅ 数据库集合数量: {len(collections)}")
            
            # 检查索引
            for collection_name in collections:
                if not collection_name.startswith('_'):
                    collection = self.db[collection_name]
                    indexes = await collection.list_indexes().to_list(length=None)
                    logger.info(f"  {collection_name}: {len(indexes)} 个索引")
            
            # 检查配置
            config_count = await self.db["system_config"].count_documents({})
            logger.info(f"✅ 系统配置项数量: {config_count}")
            
            logger.info("✅ 数据库验证完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库验证失败: {e}")
            return False
    
    async def close(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()
            logger.info("✅ 数据库连接已关闭")
    
    async def initialize(self):
        """执行完整的数据库初始化"""
        logger.info("🚀 开始初始化数据库...")
        
        try:
            # 连接数据库
            if not await self.connect():
                return False
            
            # 创建集合和索引
            await self.create_collections()
            
            # 插入初始数据
            await self.insert_initial_data()
            
            # 创建数据库信息
            await self.create_database_info()
            
            # 验证设置
            if await self.verify_setup():
                logger.info("🎉 数据库初始化完成！")
                return True
            else:
                logger.error("❌ 数据库初始化验证失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
            return False
        finally:
            await self.close()

async def main():
    """主函数"""
    # 从环境变量或默认值获取配置
    mongodb_url = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
    database_name = os.getenv("DATABASE_NAME", "llm_kit")
    
    print(f"MongoDB URL: {mongodb_url}")
    print(f"数据库名称: {database_name}")
    
    # 初始化数据库
    initializer = DatabaseInitializer(mongodb_url, database_name)
    success = await initializer.initialize()
    
    if success:
        print("\n✅ 数据库初始化成功！")
        print("现在可以启动LLM-Kit应用了。")
    else:
        print("\n❌ 数据库初始化失败！")
        print("请检查MongoDB是否正在运行，并查看错误日志。")

if __name__ == "__main__":
    asyncio.run(main())
