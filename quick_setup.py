#!/usr/bin/env python3
"""
LLM-Kit 快速环境配置脚本
"""

import os
import sys
import subprocess
import platform

def run_command(command, check=True):
    """执行命令"""
    try:
        print(f"执行: {command}")
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return e

def install_basic_dependencies():
    """安装基本依赖"""
    print("=== 安装基本依赖包 ===")
    
    basic_packages = [
        "fastapi",
        "uvicorn[standard]", 
        "motor",
        "pymongo",
        "pydantic",
        "pydantic-settings",
        "transformers",
        "accelerate",
        "sentencepiece",
        "protobuf",
        "pillow",
        "numpy",
        "pandas",
        "tiktoken",
        "pyyaml",
        "tqdm",
        "PyMuPDF",
        "loguru",
        "python-multipart",
        "requests",
        "jieba",
        "datasketch",
        "levenshtein",
        "bcrypt",
        "sqlalchemy"
    ]
    
    for package in basic_packages:
        try:
            run_command(f"pip install {package}")
            print(f"✅ {package} 安装成功")
        except:
            print(f"⚠️ {package} 安装失败，跳过...")

def create_directories():
    """创建必要的目录"""
    print("\n=== 创建项目目录 ===")
    
    directories = [
        "uploads",
        "temp", 
        "outputs",
        "logs",
        "cache",
        "models_cache",
        "mongodb_data",
        "mongodb_logs"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        except Exception as e:
            print(f"❌ 创建目录失败 {directory}: {e}")

def check_mongodb():
    """检查MongoDB连接"""
    print("\n=== 检查MongoDB ===")
    try:
        import pymongo
        client = pymongo.MongoClient('mongodb://localhost:27017', serverSelectionTimeoutMS=2000)
        client.admin.command('ping')
        print("✅ MongoDB连接正常")
        return True
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        print("请确保MongoDB正在运行")
        return False

def test_imports():
    """测试关键包导入"""
    print("\n=== 测试包导入 ===")
    
    test_packages = [
        "fastapi",
        "uvicorn", 
        "motor",
        "pymongo",
        "transformers",
        "PIL",
        "numpy",
        "pandas",
        "loguru"
    ]
    
    success_count = 0
    for package in test_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {package}: {e}")
    
    print(f"\n导入测试: {success_count}/{len(test_packages)} 成功")
    return success_count == len(test_packages)

def main():
    """主函数"""
    print("🚀 LLM-Kit 快速环境配置")
    print("=" * 50)
    
    # 检查Python版本
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 8:
        print("❌ 需要Python 3.8或更高版本")
        return
    
    # 创建目录
    create_directories()
    
    # 安装依赖
    install_basic_dependencies()
    
    # 测试导入
    imports_ok = test_imports()
    
    # 检查MongoDB
    mongodb_ok = check_mongodb()
    
    print("\n" + "=" * 50)
    print("📋 配置总结:")
    print(f"✅ 目录创建: 完成")
    print(f"{'✅' if imports_ok else '❌'} 包导入: {'成功' if imports_ok else '失败'}")
    print(f"{'✅' if mongodb_ok else '❌'} MongoDB: {'连接正常' if mongodb_ok else '需要启动'}")
    
    if imports_ok and mongodb_ok:
        print("\n🎉 基本环境配置完成！")
        print("\n📋 下一步:")
        print("1. 如果PyTorch还在安装，请等待完成")
        print("2. 运行: python main.py")
        print("3. 访问: http://127.0.0.1:8000")
    else:
        print("\n⚠️ 环境配置未完全成功，请检查上述问题")

if __name__ == "__main__":
    main()
