# MongoDB配置文件
# 适用于LLM-Kit项目

# 存储配置
storage:
  dbPath: ./mongodb_data
  journal:
    enabled: true
  engine: wiredTiger
  wiredTiger:
    engineConfig:
      cacheSizeGB: 1
      journalCompressor: snappy
      directoryForIndexes: false
    collectionConfig:
      blockCompressor: snappy
    indexConfig:
      prefixCompression: true

# 系统日志配置
systemLog:
  destination: file
  logAppend: true
  path: ./mongodb_logs/mongod.log
  logRotate: rename
  verbosity: 0

# 网络配置
net:
  port: 27017
  bindIp: 127.0.0.1
  maxIncomingConnections: 65536
  wireObjectCheck: true
  ipv6: false

# 进程管理
processManagement:
  fork: false
  pidFilePath: ./mongodb_logs/mongod.pid

# 安全配置
security:
  authorization: disabled

# 操作分析
operationProfiling:
  mode: off
  slowOpThresholdMs: 100

# 复制集配置 (单机模式下注释掉)
# replication:
#   replSetName: "rs0"

# 分片配置 (单机模式下注释掉)
# sharding:
#   clusterRole: configsvr
