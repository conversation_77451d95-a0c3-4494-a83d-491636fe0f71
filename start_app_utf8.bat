@echo off
:: Set UTF-8 encoding for proper display
chcp 65001 >nul 2>&1

:: Set console colors
color 0A

echo.
echo ==========================================
echo         LLM-Kit Application Launcher
echo ==========================================
echo.

:: Change to script directory
cd /d "%~dp0"
if errorlevel 1 (
    echo [ERROR] Failed to change to project directory
    echo [ERROR] Current directory: %cd%
    echo [ERROR] Target directory: %~dp0
    pause
    exit /b 1
)

:: Display system information
echo [SYSTEM] Checking system environment...
echo [SYSTEM] Current directory: %cd%
echo [SYSTEM] Date/Time: %date% %time%
echo.

:: Check Python environment
echo [CHECK] Verifying Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found in PATH!
    echo [ERROR] Please ensure Python is installed and added to system PATH
    echo.
    echo [HELP] To fix this issue:
    echo [HELP] 1. Install Python from https://python.org
    echo [HELP] 2. Make sure to check "Add Python to PATH" during installation
    echo [HELP] 3. Restart command prompt and try again
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo [SUCCESS] Python %%i detected
)
echo.

:: Check main program file
echo [CHECK] Verifying main application file...
if not exist "main.py" (
    echo [ERROR] main.py file not found!
    echo [ERROR] Please ensure you are running this script from the LLM-Kit project directory
    echo.
    pause
    exit /b 1
) else (
    echo [SUCCESS] main.py found
)
echo.

:: Check environment configuration
echo [CHECK] Verifying environment configuration...
if not exist ".env" (
    echo [WARNING] .env file not found
    echo [WARNING] Application will use default configuration
    echo [INFO] To customize settings:
    echo [INFO] 1. Copy .env.example to .env
    echo [INFO] 2. Edit .env file with your API keys and preferences
    echo.
) else (
    echo [SUCCESS] .env configuration file found
)

:: Create necessary directories
echo [SETUP] Creating required directories...
for %%d in (uploads temp outputs logs cache models_cache) do (
    if not exist "%%d" (
        mkdir "%%d" >nul 2>&1
        echo [CREATED] Directory: %%d
    ) else (
        echo [EXISTS] Directory: %%d
    )
)
echo.

:: Display application configuration
echo [CONFIG] Application settings:
echo [CONFIG] - Host: 127.0.0.1
echo [CONFIG] - Port: 8000
echo [CONFIG] - Project root: %cd%
echo [CONFIG] - Python executable: 
python -c "import sys; print('[CONFIG] - Python path: ' + sys.executable)"
echo.

:: Check MongoDB connection
echo [CHECK] Testing MongoDB connection...
python -c "import pymongo; client = pymongo.MongoClient('mongodb://localhost:27017', serverSelectionTimeoutMS=3000); client.admin.command('ping'); print('[SUCCESS] MongoDB connection established')" 2>nul
if errorlevel 1 (
    echo [ERROR] MongoDB connection failed!
    echo [ERROR] MongoDB server is not running or not accessible
    echo.
    echo [SOLUTION] To fix this issue:
    echo [SOLUTION] 1. Start MongoDB by running: start_mongodb.bat
    echo [SOLUTION] 2. Wait for MongoDB to fully start
    echo [SOLUTION] 3. Run this script again
    echo.
    echo [OPTION] Press any key to continue without MongoDB ^(some features may not work^)
    echo [OPTION] Or close this window and start MongoDB first
    pause
    echo.
) else (
    echo [SUCCESS] MongoDB is running and accessible
    echo.
)

:: Final startup confirmation
echo [READY] All checks completed. Starting LLM-Kit application...
echo.
echo ==========================================
echo            Application Access URLs
echo ==========================================
echo [WEB] Main Application: http://127.0.0.1:8000
echo [API] API Documentation: http://127.0.0.1:8000/docs
echo [API] Interactive API: http://127.0.0.1:8000/redoc
echo [OCR] OCR Status: http://127.0.0.1:8000/ocr/status
echo ==========================================
echo.
echo [INFO] Press Ctrl+C to stop the application
echo [INFO] Application logs will be displayed below
echo.

:: Start the application
echo [STARTING] Launching main application...
echo.
python main.py

:: Handle application exit
echo.
echo ==========================================
echo [STOPPED] LLM-Kit application has stopped
echo ==========================================
echo.
echo [INFO] If the application stopped unexpectedly, check the error messages above
echo [INFO] Common issues and solutions:
echo [INFO] - Port 8000 already in use: Stop other applications using this port
echo [INFO] - Import errors: Run 'pip install -r requirements.txt'
echo [INFO] - MongoDB errors: Ensure MongoDB is running
echo.
echo [INFO] Press any key to exit...
pause >nul
